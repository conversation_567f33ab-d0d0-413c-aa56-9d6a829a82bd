<?php

/**
 * Obsługa stron tekstowych
 * @package Application
 * @subpackage Modules
 */
class Pages extends Module
{
    public $app       = null;
    public $urlpram   = null;
    public $lang   = null;
    public $currency   = null;
    public $topparams = array();
    protected $en      = null;
    
    private $pagecache = array();
    private $pagescache = array();
    private $sectionscache = array();
    private $pagesallcache = array();

    public function __construct( $app = null ) 
    {
        $this->app = &$app;
        $this->en = Database::activate();
        $this->mgo = Mymongo::activate();
        $this->getOfferMeta();
        $this->app->setTranslations($this->lang);
        $this->app->SetLang($this->lang);
//        if($this->lang != LANG){}
        switch ( $app->urlparam['a'] )
        {            
            case 'page': $this->DisplayPage(); break;
            default: break;
        }   
    }

    public function getOfferMeta()
    {
        $where = array(
            'status.id'       => 5,
            'current'         => 1,
            'partnerzy_sp.id' => INSTALLATION,
        );

        $cursor = $this->mgo->db->offers->findOne($where);
        $currentoffer = $this->mgo->Mgo2array($cursor);
        $this->meta = $currentoffer['meta'];
        $this->lang = $currentoffer['lang'] ?: $_SESSION['LANG'];
        if(!defined('LANG')){
            define('LANG', $this->lang);
        }
        $this->currency = $currentoffer['currency'] ?: $_SESSION['CURRENCY'];
//        die(var_dump($this->lang, $this->currency));
        $this->app->SetCurrency($this->currency, true, 1);
        $this->topparams['offerid'] = $currentoffer['parentid'];
    }
     
    /**
     * Generuje stronę tekstową
     */
    private function DisplayPage()
    {
	$id = intval($_GET['id']);
	$dane = $this->GetPageData($id);
	if(count($dane['cf']['faq'] ?? [])>0) {
		foreach($dane['cf']['faq'] as $k=>$vcf) {
			if(count($vcf['naglowek'] ?? [])>0) {
				$dane['cf']['faq'][$k]['naglowek'] = $dane['cf']['faq'][$k]['naglowek'][$this->lang];
			}
			if(count($vcf['pytanie'] ?? [])>0) {
				$dane['cf']['faq'][$k]['pytanie'] = $dane['cf']['faq'][$k]['pytanie'][$this->lang];
			}
			if(count($vcf['odpowiedz'] ?? [])>0) {
				$dane['cf']['faq'][$k]['odpowiedz'] = $this->FixPaths($dane['cf']['faq'][$k]['odpowiedz'][$this->lang]);
			}
		}
	}

	if(count($dane['cf']['faq1'] ?? [])>0) {
		foreach($dane['cf']['faq1'] as $k=>$vcf) {
			if(count($vcf['naglowek'] ?? [])>0) {
				$dane['cf']['faq1'][$k]['naglowek'] = $dane['cf']['faq1'][$k]['naglowek'][$this->lang];
			}
			if(count($vcf['pytanie'] ?? [])>0) {
				$dane['cf']['faq1'][$k]['pytanie'] = $dane['cf']['faq1'][$k]['pytanie'][$this->lang];
			}
			if(count($vcf['odpowiedz'] ?? [])>0) {
				$dane['cf']['faq1'][$k]['odpowiedz'] = $this->FixPaths($dane['cf']['faq1'][$k]['odpowiedz'][$this->lang]);
			}
		}
	}

	if(count($dane['cf']['faq2'] ?? [])>0) {
		foreach($dane['cf']['faq2'] as $k=>$vcf) {
			if(count($vcf['naglowek'] ?? [])>0) {
				$dane['cf']['faq2'][$k]['naglowek'] = $dane['cf']['faq2'][$k]['naglowek'][$this->lang];
			}
			if(count($vcf['pytanie'] ?? [])>0) {
				$dane['cf']['faq2'][$k]['pytanie'] = $dane['cf']['faq2'][$k]['pytanie'][$this->lang];
			}
			if(count($vcf['odpowiedz'] ?? [])>0) {
				$dane['cf']['faq2'][$k]['odpowiedz'] = $this->FixPaths($dane['cf']['faq2'][$k]['odpowiedz'][$this->lang]);
			}
		}
	}
	if(SCONTEXT == 'front' AND $id == 63 AND !empty($this->meta['page']['contact'][intval($this->lang)])){
	    $content = nl2br($this->meta['page']['contact'][intval($this->lang)]);
//        die(var_dump($this->meta));
        $danereturn = array(
            "id"=>$dane['id'],
            "nazwa"=>$dane['nazwa'][intval($this->lang)],
            "content"=>$content,
            "cf"=>$dane['cf']
        );
    } else {
        $danereturn = array(
            "id"=>$dane['id'],
            "nazwa"=>$dane['nazwa'][intval($this->lang)],
            "content"=>$this->FixPaths($dane['content'][intval($this->lang)]),
            "cf"=>$dane['cf']
        );
    }

        $this->topparams['meta']['title'] = $dane['nazwa'][intval($this->lang)];
        $this->ProductTpl( $dane['template'], $danereturn );
    }
    
    protected function GetPageData($id) {
	$dane = array();
	if($id>0) {
		if(isset($this->pagecache[$id])) return $this->pagecache[$id];
		
		$dane=$this->en->select_r("SELECT * FROM ".TABLE_PREFIX."pages WHERE id=".$id);
		$dane['nazwa']=array();
		$dane['content']=array();
		$res=$this->en->select("SELECT * FROM ".TABLE_PREFIX."pages_lang WHERE item=".$id);
		if(count($res)>0) {
			foreach($res as $r) {
				$dane['nazwa'][$r['lang_id']] = $r['nazwa'];
				$dane['content'][$r['lang_id']] = $r['content'];
			}
		}
		$cf = $this->GetCustomFields($id);
		$dane['cf'] = $cf;
		
		$this->pagecache[$id] = $dane;
	}
	return $dane;
    }
    
    protected function GetPageBySection($section) {
	//check section type
	if(isset($this->sectionscache[$section])) $sect = $this->sectionscache[$section];
	else {
		$sect =$this->en->get_filtered_rows('pages_sections','*',array(array('section',$section,'STRING')));
		$this->sectionscache[$section] = $sect;
	}
	$return = array();
	
	//get pages
	if(isset($this->pagescache[$sect[0]['id']])) $pages = $this->pagescache[$sect[0]['id']];
	else {
		$pages =$this->en->get_filtered_rows('pages','*',array(array('section',$sect[0]['id'],'INT'),array('status',1,'INT')));
		$this->pagescache[$sect[0]['id']] = $pages;
	}
	
	switch($sect[0]['stype']) {
		case 'multi': 
			if($pages[0]['id'] > 0) {
				foreach($pages as $page) {
					$return[] = $this->GetPageData($page['id']);
				}
			}
			break;
		case 'single': 
			if($pages[0]['id'] > 0) $return = $this->GetPageData($pages[0]['id']);
			break;
	}
	return $return;
    }
    
    public function GetPage($section, $field = false)
    {
        $dane = $this->GetPageBySection($section);
        
        if($dane['id']>0) {
		//single
		if(count($dane['cf']['faq'] ?? [])>0) {
			foreach($dane['cf']['faq'] as $k=>$vcf) {
				if($vcf['naglowek'] && count($vcf['naglowek'] ?? [])>0) {
					$dane['cf']['faq'][$k]['naglowek'] = $dane['cf']['faq'][$k]['naglowek'][$this->lang];
				}
				if(count($vcf['pytanie'] ?? [])>0) {
					$dane['cf']['faq'][$k]['pytanie'] = $dane['cf']['faq'][$k]['pytanie'][$this->lang];
				}
				if(count($vcf['odpowiedz'] ?? [])>0) {
					$dane['cf']['faq'][$k]['odpowiedz'] = $this->FixPaths($dane['cf']['faq'][$k]['odpowiedz'][$this->lang]);
				}
			}
		}
		
		if(count($dane['cf']['faq1'] ?? [])>0) {
			foreach($dane['cf']['faq1'] as $k=>$vcf) {
				if(count($vcf['naglowek'] ?? [])>0) {
					$dane['cf']['faq1'][$k]['naglowek'] = $dane['cf']['faq1'][$k]['naglowek'][$this->lang];
				}
				if(count($vcf['pytanie'] ?? [])>0) {
					$dane['cf']['faq1'][$k]['pytanie'] = $dane['cf']['faq1'][$k]['pytanie'][$this->lang];
				}
				if(count($vcf['odpowiedz'] ?? [])>0) {
					$dane['cf']['faq1'][$k]['odpowiedz'] = $this->FixPaths($dane['cf']['faq1'][$k]['odpowiedz'][$this->lang]);
				}
			}
		}
		
		if(count($dane['cf']['faq2'] ?? [])>0) {
			foreach($dane['cf']['faq2'] as $k=>$vcf) {
				if(count($vcf['naglowek'] ?? [])>0) {
					$dane['cf']['faq2'][$k]['naglowek'] = $dane['cf']['faq2'][$k]['naglowek'][$this->lang];
				}
				if(count($vcf['pytanie'] ?? [])>0) {
					$dane['cf']['faq2'][$k]['pytanie'] = $dane['cf']['faq2'][$k]['pytanie'][$this->lang];
				}
				if(count($vcf['odpowiedz'] ?? [])>0) {
					$dane['cf']['faq2'][$k]['odpowiedz'] = $this->FixPaths($dane['cf']['faq2'][$k]['odpowiedz'][$this->lang]);
				}
			}
		}
		
		
		$danereturn = array(
			"id"=>$dane['id'],
			"nazwa"=>$dane['nazwa'][intval($this->lang)],
			"content"=>$this->FixPaths($dane['content'][intval($this->lang)]),
			"cf"=>$dane['cf']
		);
        } else {
		$danereturn = array();
		for($i=0;$i<count($dane);$i++) {
			$danereturn[] = array(
				"id"=>$dane[$i]['id'],
				"nazwa"=>$dane[$i]['nazwa'][intval($this->lang)],
				"content"=>$this->FixPaths($dane[$i]['content'][intval($this->lang)]),
				"cf"=>$dane[$i]['cf'],
				"template"=>$dane[$i]['template']
			);
		}
        }
        
        if($field !== false) {
		return $danereturn[$field] ? : false;
        }
        return $danereturn ? : '';
    }
    
    public function GetPageList($section) {
	$return = '';
	$pages = $this->GetPage($section);
	for($i=0;$i<count($pages);$i++) {
	    $link = SITE_URL.'pages/page?id='.$pages[$i]['id'];
	    if($pages[$i]['template'] == "link") $link = $pages[$i]['cf']['url'];
	    $return .= '<li><a href="'.$link.'">'.$pages[$i]['nazwa'].'</a></li>';
	}
	return $return;
    }
    
    protected function GetCustomFields($page_id) {
	    $return = array();
	    $cf = $this->en->select("SELECT * FROM ".TABLE_PREFIX."pages_custom_fields WHERE page_id=".$page_id);
	    if(count($cf) > 0) {
		    foreach($cf as $item) {
			    $return[$item['variable']] = unserialize(base64_decode($item['value']));
		    }
	    }
	    return $return;
    }
    
    private function FixPaths($content) {
	    $f = array("data/");
	    $r = array(SITE_URL."data/");
	    
	    $content = $this->RenderTags($content);
	    return str_replace($f,$r,$content);
    }
    
    private function RenderTags($content) {
	    $f = array('%ESKIPASS_HOME%','%SITE_URL%');
	    $r = array('<a href="http://e-skipass.pl">e-skipass.pl</a>','<a href="'.SITE_URL.'">'.SITE_URL.'</a>');
	    $content = str_replace($f,$r,$content);
	    
	    if(count($this->pagesallcache) > 0) {
		    $pages = $this->pagesallcache;
	    } else {
		    $pages = $this->en->select("SELECT * FROM ".TABLE_PREFIX."pages");
		    $this->pagesallcache = $pages;
	    }
	    foreach($pages as $p) {
		    $page = $this->GetPageData($p['id']);
		    $f = array('%PAGE_'.$p['id'].'%');
		    $r = array('<a href="'.SITE_URL.'pages/page?id='.$p['id'].'">'.$page['nazwa'][intval($this->lang)].'</a>');
		    $content = str_replace($f,$r,$content);
	    }
	    
	    return $content;
    }
    
    private function ProductTpl( $tpl, $data ) {
        $this->app->AddWidgetsData( 'topnav', $this->meta['header'] );
        $this->app->AddWidgetsData( 'footer', $this->meta['footer'] );
        $this->app->AddWidgetsData( 'header', $this->topparams);
        $this->app->AddWidgetsData( 'accountnav' );
        $this->app->AddWidgetsData( 'basket' );
        $this->app->AddDecoratorData( 'dane', $data );
        $this->app->SetTemplate( 'modules/pages/' . $tpl . '.tpl');
    }
}