<?php

/**
 * @@@Admin Clients@@@
 * Metody do uprawnień:
 * @@list:Display users list,details:List and details clients@@
 *
 * @package Administrator
 * @subpackage Modules
 * 
 */


class Details {

    private $headerparams = array();
    private $topparams = array();
    private $adminnavparams = array();
    private $footerparams = array();
    protected $error;
    protected $en = null;
    protected $mgo = null;
    public static $_produkty;
    public static $_oferty;
    public static $_karty;
    public static $_karnety;
    public static $_vouchery;
    public static $_vprodukty;
    public static $_ubezpieczenia;

    public function __construct($app) {
        $this->app = &$app;
        $this->en = Database::activate();
    }

    public static function _staticGetOfferProducts($offer){
        if(!self::$_oferty[$offer]){
        $mgo = Mymongo::activate();
        $cursor = $mgo->mget_offer_details($offer);
        if(is_null($cursor['karty'])) {
            $cursor['karty'] = array();
        }
        foreach($cursor['karty'] as $line){
            self::$_oferty[$offer]['karty'][$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_karty[$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_produkty[$line['identyfikator']] = array('typ'=>'Karta','nazwa'=>$line['nazwa'][LANG]);
        }
        
        if(is_null($cursor['karnety'])) {
            $cursor['karnety'] = array();
        }
        foreach($cursor['karnety'] as $line){
            self::$_oferty[$offer]['karnety'][$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_karnety[$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_produkty[$line['identyfikator']] = array('typ'=>'Karnet','nazwa'=>$line['nazwa'][LANG]);
        }
        
        if(is_null($cursor['ubezpieczenia'])) {
            $cursor['ubezpieczenia'] = array();
        }
        foreach($cursor['ubezpieczenia'] as $line){
            self::$_oferty[$offer]['ubezpieczenia'][$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_ubezpieczenia[$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_produkty[$line['identyfikator']] = array('typ'=>'Ubezpieczenie','nazwa'=>$line['nazwa'][LANG]);
        }
        
        if(is_null($cursor['vouchery'])) {
            $cursor['vouchery'] = array();
        }
        foreach($cursor['vouchery'] as $line){
            self::$_oferty[$offer]['vouchery'][$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_vouchery[$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_produkty[$line['identyfikator']] = array('typ'=>'Voucher','nazwa'=>$line['nazwa'][LANG]);
        }
        if(is_null($cursor['produkty_wirtualne'])) {
            $cursor['produkty_wirtualne'] = array();
        }
        
        foreach($cursor['produkty_wirtualne'] as $line){
            self::$_oferty[$offer]['produkty_wirtualne'][$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_vprodukty[$line['identyfikator']] = $line['nazwa'][LANG];
            self::$_produkty[$line['identyfikator']] = array('typ'=>'Produkt wirtualny','nazwa'=>$line['nazwa'][LANG]);
        }
        
        self::$_oferty[$offer]['nazwa'] = $cursor['nazwa'][LANG];
        unset($cursor);
        }

    }


    
    public function AjaxGetHistory() {
        $cid = $_POST['serial'];
        if(0 === preg_match('/\d*/',$cid)){
            $res['status'] = 'error';
            $res['errormsg'] = 'Bad ID';
            echo json_encode($res);
            die();
        }
        if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_POST['basedate'])) {
            $date = date('Y-m-d', time());
        }
        else {
            $date = $_POST['basedate'];
        }
        if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_POST['finishdate'])) {
            $datee = $date;
        }
        else {
            $datee = $_POST['finishdate'];
        }
        $dataod = $date.' 00:00:01';
        $datado = $datee.' 23:59:59';
        $hs = Karta::GetHistoryBs($cid, $dataod, $datado);
        if(false === $hs){
            $res['status'] = 'error';
            $res['errormsg'] = 'Connection Error';
            echo json_encode($res);
            die();
        }
        $res['status'] = 'ok';
        $res['data'] = $hs;
        echo json_encode($res);
        die();
    }
    
    public function AjaxGetCardStatus() {
        $cid = $_POST['serial'];
        if(0 === preg_match('/\d*/',$cid)){
            $res['status'] = 'error';
            $res['errormsg'] = 'Bad ID';
            echo json_encode($res);
            die();
        }
        $hs = Karta::GetStatusBs($cid);
        if(false === $hs){
            $res['status'] = 'error';
            $res['errormsg'] = 'Connection Error';
            echo json_encode($res);
            die();
        }
        $res['status'] = 'ok';
        $bilety = array();
        if(isset($hs['BILETY'])){
            foreach($hs['BILETY'] as $v){
                list($nazwa,$biletnr,$waznoscdni,$waznosctermin,$wydany,$uzyty,$punkty,$typ) = explode(';',$v);
                $bilet['NAZWA'] = $nazwa;
                $bilet['BILETNR'] = $biletnr;
                $bilet['WAZNOSCDNI'] = $waznoscdni;
                $bilet['WAZNOSCTERMIN'] = $waznosctermin;
                $bilet['WYDANY'] = $wydany;
                $bilet['UZYTY'] = $uzyty;
                $bilety[] = $bilet;
                unset($bilet);
            }
        }
        $hs['BILETY'] = $bilety;
        $res['data'] = $hs;
        echo json_encode($res);
        die();
    }
    
    /**
     * zmiana statusu biletu na 0
     */
    public function AjaxDeleteTicketEski() {
        $cid = $_POST['serial'];
        $tid = $_POST['tid'];
        if(0 === preg_match('/\d*/',$cid) OR 0 === preg_match('/\d*/',$tid)){
            $res['status'] = 'error';
            $res['errormsg'] = 'Bad ID';
            echo json_encode($res);
            die();
        }
        $sql = 'UPDATE '.TABLE_PREFIX.'karnety SET status = 0 WHERE id='.$tid.' AND serial_karty="'.$cid.'"';
        $this->en->query($sql);
        $ar = $this->en->affected_rows();
        if(0 === $ar){
            $res['status'] = 'error';
            $res['errormsg'] = 'Nie zaktualizowano żadnego biletu';
            echo json_encode($res);
            die();
        }
        $res['status'] = 'ok';
        $res['data'] = array('cid'=>$cid, 'tid'=>$tid);
        $this->app->LogInsert('DELETEESKI',$tid,'Kasowanie biletu '.$tid.' z karty: '.$cid);
        echo json_encode($res);
        die();
    }
    
    /**
     * kasowanie karnetu z karty w BS
     */
    public function AjaxDeleteTicketBS() {
        $cid = $_POST['serial'];
        $tid = $_POST['tid'];
        if(0 === preg_match('/\d*/',$cid) OR 0 === preg_match('/\d*/',$tid)){
            $res['status'] = 'error';
            $res['errormsg'] = 'Bad ID';
            echo json_encode($res);
            die();
        }
        $ar = Karta::DelTicketBs($cid, $tid);
        if(false === $ar){
            $res['status'] = 'error';
            $res['errormsg'] = 'Błąd połączenia';
            echo json_encode($res);
            die();
        }
        if($ar['STATUS'] != 0){
            $res['status'] = 'error';
            $res['errormsg'] = 'Błędny status: '.$ar['STATUS'].', info: '.$ar['OPIS'];
            echo json_encode($res);
            die();
        }
        $res['status'] = 'ok';
        $res['data'] = array('cid'=>$cid, 'tid'=>$tid,'answer'=>$ar);
        $this->app->LogInsert('DELETEBS',$ar['IDOP'],'Rozkaz 1d, kasowanie biletu '.$tid.' z karty: '.$cid);
        echo json_encode($res);
        die();
    }

    
    public function Karnetinfo(){
        $tid = intval($_POST['id']);
        $kt = $this->getRawTicket($tid);
        $karnet = $kt['base'];
        $uzycie = $kt['zuzycie'];
        $kid = $karnet['id_karty'];
        $karta = $this->getRawKarta($kid);
//        $uzycie = $this->en->select('SELECT * FROM '.TABLE_PREFIX.'karnety_zuzycie WHERE id_karnetu='.$tid);
        $user = User::GetUserData($karnet['id_usera']);
        $mgo = Mymongo::activate();
        $cursor = $mgo->db->offers->findOne(array('current'=>1,'karnety.identyfikator'=>$karnet['id_produktu_oferta']));
        $oferta = $mgo->Mgo2array($cursor);
//        die(var_dump($oferta));
        foreach($oferta['karnety'] as $k){
            if($k['identyfikator'] == $karnet['id_produktu_oferta']){
                $karnet['nazwa'] = $k['nazwa'][LANG];
                $karnet['nazwa_oferty'] = $oferta['nazwa'][LANG];
                break;
            }
        }
        unset($karnet['mozliwe_ubezpieczenie']);
        unset($karnet['id_promocji']);
        unset($user['base']['md5_haslo']);
        unset($user['base']['md5_pin']);
        unset($user['base']['pin']);
        unset($user['base']['signature']);
        unset($user['base']['lostpwd']);
        unset($user['base']['lostpwdts']);
        unset($user['base']['dostepapitask']);
        unset($karta['id_oferty']);
        unset($karta['id_usera']);
        if($karta['serial_tmp']!=''){
            $karta['serial'] .=" ({$karta['serial_tmp']})";
        }
        unset($karta['serial_tmp']);
        if($karnet['ubezpieczenie'] == 1){
           $this->app->ADD('ubezpieczenie',$this->getRawInsurance(false, $karnet['id']));
        }
        include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');
        $this->app->ADD('karnet',$karnet);
        $this->app->ADD('karta',$karta);
        $this->app->ADD('user_base',$user['base']);
        $this->app->ADD('uzycie',$uzycie);
        $this->app->SetTemplate('modules/details/karnetinfo.tpl');
        $dekor = new DecoratorSmarty($this->app);
        $out = $dekor->output;
        $resp['panetitle'] = App::_Lang('Podgląd karnetu').': '.$karnet['nazwa'];
        $resp['panebody'] = $out;
        echo json_encode($resp);
        die();
    }
    
    public function Kartainfo(){
        $kid = intval($_POST['id']);
        include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');
        $karta = $this->getRawKarta($kid);
        $user = User::GetUserData($karta['id_usera']);
        $sql = 'SELECT id FROM ts_karnety WHERE id_karty='.$kid.' ORDER BY id DESC';
        $ef = $this->en->select($sql);
        $lk = count($ef);
        if($lk>0){
            $mgo = Mymongo::activate();
            foreach($ef as $v){
                $zw = $this->getRawTicket($v['id']);
                $karnet = $zw['base'];
                $cursor = $mgo->db->offers->findOne(array('current'=>1,'karnety.identyfikator'=>$karnet['id_produktu_oferta']));
                $oferta = $mgo->Mgo2array($cursor);
                if($oferta['karnety']){
                foreach($oferta['karnety'] as $k){
                    if($k['identyfikator'] == $karnet['id_produktu_oferta']){
                        $karnet['nazwa'] = $k['nazwa'][LANG];
                        $karnet['nazwa_oferty'] = $oferta['nazwa'][LANG];
                        break;
                    }
                }
                }
                $karnety[] = $karnet;
            }
        }
        $this->app->ADD('ilosckarnetow',$lk);
        $this->app->ADD('karnety',$karnety);
        $this->app->ADD('karta',$karta);
        $this->app->ADD('user_base',$user['base']);
        $this->app->SetTemplate('modules/details/kartainfo.tpl');
        $dekor = new DecoratorSmarty($this->app);
        $out = $dekor->output;
        $resp['panetitle'] = App::_Lang('Podgląd karty').': '.$karta['id'];
        $resp['panebody'] = $out;
        echo json_encode($resp);
        die();
    }
    
    public function Voucherinfo(){
        $kid = intval($_POST['id']);
        include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');
        $karta = $this->getRawVoucher($kid);
        $user = User::GetUserData($karta['id_usera']);
        $mgo = Mymongo::activate();
        $cursor = $mgo->db->offers->findOne(array('current'=>1,'vouchery.identyfikator'=>$karta['id_produktu_oferta']));
        $oferta = $mgo->Mgo2array($cursor);
        if($oferta['vouchery']){
        foreach($oferta['vouchery'] as $k){
            if($k['identyfikator'] == $karta['id_produktu_oferta']){
                $karta['nazwa'] = $k['nazwa'][LANG];
                $karta['nazwa_oferty'] = $oferta['nazwa'][LANG];
                break;
            }
        }
        }
//        die(var_dump($karta));
        $this->app->ADD('karta',$karta);
        $this->app->ADD('user_base',$user['base']);
        $this->app->SetTemplate('modules/partner/details/voucherinfo.tpl');
        $dekor = new DecoratorSmarty($this->app);
        $out = $dekor->output;
        $resp['panetitle'] = App::_Lang('Podgląd vouchera').': '.$karta['id'];
        $resp['panebody'] = $out;
        echo json_encode($resp);
        die();
    }

    public function ReturnVoucher(string $ptype)
    {
        switch ($ptype){
            case 'voucher':
                $vid = new \App\Models\Voucher();
                break;
            case 'ticket':
                $vid = new \App\Models\Ticket();
                break;
            default:
                return false;
        }

        $topup = new \App\Models\TopupLog();
        $vid->findOne((int)$_POST['id']);
        $uid = User::GetUserID();

        $service = new \App\Services\ExchangeTicketCodesService($this->app);
        if($service->requestDelete($vid->tid, null))
        {
            $this->MakeReturnLog($vid->tid, $uid, $vid->id_usera, $ptype);
            return true;
        } else {
            return false;
        }

    }

    public function MakeReturnLog($tid, $uid, $uzid, $ptype)
    {
        $tidsql = 'SELECT * FROM exchange_tid WHERE tid ="'.$tid.'"';
        $_tid = $this->en->select_r($tidsql);
        $exch = $this->en->select_r('SELECT * FROM exchange_ticket_codes WHERE tid_id='.$_tid['id']);

        $log_SQL = "INSERT INTO topup_log (operator_id, etc_id, order_id, uzytkownik_id, bsid, card_serial, reason, comment, p_type) VALUES ("
            .(int)$uid. //operator_id
            ",".(int)$exch['exchange_ticket_code_id']. //etc_id
            ",".$exch['order_id']. //order_id
            ",".(int)$uzid. //uzytkownik_id
            ",".$exch['baseid']. //bsid
            ",".$exch['nr']. //card_serial
            ","."4". //reason
            ","."'Skasowany przez partnera'". //comment
            ","."'".$ptype."'". ")"; //p_type

        $this->en->query($log_SQL);
    }
    
    public function Insuinfo(){
        $kid = intval($_POST['id']);
        include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');
        $karta = $this->getRawInsurance($kid);
//        $user = User::GetUserData($karta['id_usera']);
        $this->app->ADD('ubezpieczenie',$karta);
//        $this->app->ADD('user',$user);
        $this->app->SetTemplate('modules/details/ubezpieczenie.tpl');
        $dekor = new DecoratorSmarty($this->app);
        $out = $dekor->output;
        $resp['panetitle'] = App::_Lang('Podgląd ubezpieczenia').': '.$karta['id'];
        $resp['panebody'] = $out;
        echo json_encode($resp);
        die();
    }
    
    public function getRawKarta($id){
        $out = $this->en->select_r('SELECT k.*, pk.shortname, ps.shortname as sprzedawca FROM '.TABLE_PREFIX.'karty k '
                . 'LEFT JOIN '.TABLE_PREFIX.'partners pk ON pk.id = k.producent_karty '
                . 'LEFT JOIN '.TABLE_PREFIX.'partners ps ON ps.id = k.installation WHERE k.id='.$id);
        return $out;
    }
    
    public function getRawVoucher($id){
        $out = $this->en->select_r('SELECT k.*, ps.shortname as sprzedawca, zp.zwrot, zz.id_ticket FROM '.TABLE_PREFIX.'vouchery k '
                . 'LEFT JOIN '.TABLE_PREFIX.'partners ps ON ps.id = k.installation '
                . 'LEFT JOIN '.TABLE_PREFIX.'zamowienia_przychod zp ON zp.typ_produktu = "vouchery" AND k.id_zamowienia = zp.id_zamowienia AND zp.parent_id = k.id '
                . 'LEFT JOIN '.TABLE_PREFIX.'zamowienia_zwroty zz ON zz.id = zp.zwrot '
                . 'WHERE k.id='.$id);
        return $out;
    }
    
    public function getRawTicket($id){
        $base = $this->en->select_r('SELECT k.*, ps.shortname FROM '.TABLE_PREFIX.'karnety k LEFT JOIN '.TABLE_PREFIX.'partners ps ON ps.id = k.installation WHERE k.id='.$id);
        $zuzycie = $this->en->select('SELECT * FROM '.TABLE_PREFIX.'karnety_zuzycie WHERE id_karnetu='.$id);
        return array('base'=>$base, 'zuzycie'=>$zuzycie);
    }
    
    public function getRawInsurance($id = false, $idkarnetu = false){
        if(false !== $id){
        $ub = $this->en->select_r('SELECT k.*, ps.shortname FROM '.TABLE_PREFIX.'ubezpieczenia k LEFT JOIN '.TABLE_PREFIX.'partners ps ON ps.id = k.installation WHERE k.id='.$id);
        $zuzycie = $this->en->select('SELECT * FROM '.TABLE_PREFIX.'karnety_zuzycie WHERE id_karnetu='.$ub['id_karnetu']);
        $r = Tools::GetPartnerProductDataHash($ub['produkt_hash']);
        $ubezp = Tools::GetPartnerProductLangs($r['id']);
        $ub['produkt_nazwa'] = $ubezp['nazwa'][LANG];
        }elseif(false !== $idkarnetu){
            $ub = $this->en->select_r('SELECT k.*, ps.shortname FROM '.TABLE_PREFIX.'ubezpieczenia k LEFT JOIN '.TABLE_PREFIX.'partners ps ON ps.id = k.installation WHERE k.id_karnetu='.$idkarnetu);
            $zuzycie = $this->en->select('SELECT * FROM '.TABLE_PREFIX.'karnety_zuzycie WHERE id_karnetu='.$idkarnetu);
            $r = Tools::GetPartnerProductDataHash($ub['produkt_hash']);
            $ubezp = Tools::GetPartnerProductLangs($r['id']);
            $ub['produkt_nazwa'] = $ubezp['nazwa'][LANG];
        }
        $user = User::GetUserData($ub['id_usera']);
        return array('base'=>$ub, 'zuzycie'=>$zuzycie,'user'=>$user);
    }

    private function makeWidgets() {
        $this->app->AWD('header', $this->headerparams);
        $this->app->AWD('top', $this->topparams);
        $this->app->AWD('adminnav', $this->adminnavparams);
        $this->app->AWD('footer', $this->footerparams);
    }

}
