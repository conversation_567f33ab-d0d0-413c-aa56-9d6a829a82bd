<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */


class Gf extends Module{
    public $app      = null;
    public $en       = null;
    public $alerts   = array();
    public $postback = array();
    public $tpl_data = null;
    
    public function __construct($app) {
        $this->app =&$app;
        $this->en = Database::activate();   
        $this->tpl_data['error']= false;
        $patt = '/([a-zA-Z0-9\-\=]*)/';
        if(1 !== preg_match($patt, $this->app->urlparam['a'],$m)){
            $this->ErrorOutput('404', 'NothingHere');
//            header('HTTP/1.1 404 Not Found');
//            die();
        }
        $data = Crypt_SSL::decodeLink($m[1]);
        if (empty($data)) {
            $this->ErrorOutput('404', 'EmptySpaces');
        }
        $id = intval($data[2]);
        $prefix= '';
        $this->GetFile($data[1], $id, $prefix, $data);
    }
    
    public function GetFile($table, $properties, $prefix = false, $data = []){
        switch($table){
            case 'voucher_serial':
                $id = $this->en->get_row('vouchery',$properties, 'serial');
                if(false == $id){
                    $this->ErrorOutput('404','BUR');
                }
                if('' == $id['pdf']){
                    $this->ErrorOutput('404','NoFile');
                }
                if(User::GetUserID() != $id['id_usera'] && $data['context'] == 'front'){
                    $this->ErrorOutput('404','BUN');
                }
                if($prefix){
                    $ap = explode('/', $id['fpath']);
                    $l = count($ap);
                    $ap[$l-1] = $prefix.$ap[$l-1];
                    $id['fpath'] = implode('/',$ap);
                }
                $this->OutputFile($id['pdf'], 'voucher.pdf');
                break;
            case 'partners_meta':
                $meta = Partner::GetPartnerMetadata(INSTALLATION);
                $fname = $meta[$properties];
                $this->FlushFile($fname);
                break;
            case 'voucher':
            case 'vouchery':
                $id = $this->en->get_row('vouchery',$properties);
                if(false == $id){
                    $this->ErrorOutput('404','BUR');
                }
                if('' == $id['pdf']){
                    $this->ErrorOutput('404','NoFile');
                }
                if(User::GetUserID() != $id['id_usera'] && $data['context'] == 'front'){
                    $this->ErrorOutput('404','BUN');
                }
                if($prefix){
                    $ap = explode('/', $id['fpath']);
                    $l = count($ap);
                    $ap[$l-1] = $prefix.$ap[$l-1];
                    $id['fpath'] = implode('/',$ap);
                }
                $this->OutputFile($id['pdf'], 'voucher_' . $id['serial_karty'] . '.pdf');
                break;

            case 'karty_voucher':
                $link = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX.'karty_voucher WHERE id=' . (int) $properties);
                if(empty($link['pdf'])){
                    $this->ErrorOutput('404','Voucher File not found or inaccessible!');
                }
                $this->OutputFile($link['pdf'], 'voucher_'. $link['serial'] . '.pdf');
                break;

            case 'vprodukty':
                $id = $this->en->get_row('vprodukty',$properties);
                if(false == $id){
                    $this->ErrorOutput('404','BUR');
                }
                if('' == $id['pdf']){
                    $this->ErrorOutput('404','NoFile');
                }
                if(User::GetUserID() != $id['id_usera'] && $data['context'] == 'front'){
                    $this->ErrorOutput('404','BUN');
                }
                $this->OutputFile($id['pdf'], 'voucher.pdf');
                break;

            case 'rozliczenia_partner_wp':
                $rozliczeniewp = $this->en->get_row('rozliczenia_partner_wp',$properties);
                $this->OutputFile($rozliczeniewp['linkfaktury'], 'voucher.pdf');
                break;

            case 'rozliczenia':
                $rozliczenie = $this->en->get_row('rozliczenia',$properties);
                $this->OutputFile($rozliczenie['plik'], 'rozliczenie_'.str_replace('-','_',$rozliczenie['nazwa']) . '.pdf');
                break;

            case 'rozliczenie_faktura':
                $link = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX.'rozliczenia WHERE id=' . intval($properties));
                if(empty($link['faktura'])){
                    die('Invoice not found or inaccessible!');
                }
                $this->OutputFile($link['faktura'], 'faktura_rozliczenie_'. str_replace('-', '_', $link['nazwa']).'.pdf');
                break;

            case 'rozliczenie_faktura_kupony':
                $id = $data[2];
                $link = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX.'rozliczenia WHERE id=' . intval($properties));
                if(empty($link['faktura_kupony'])){
                    die('Invoice not found or inaccessible!');
                }
                $this->OutputFile($link['faktura_kupony'], 'faktura_rozliczenie_kupony_'. str_replace('-', '_', $link['nazwa']).'.pdf');
                break;
            case 'faktura':
                $id = $data[2];
                $link = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX.'faktury WHERE id=' . intval($id));
                if(empty($link['pdf'])){
                    die('Invoice not found or inaccessible!');
                }
                return Additional::output_file($link['pdf'], 'faktura_'. str_replace('/', '-', $link['numer_fv'].'.pdf'));
                break;
            case 'warunki_ubezpieczenia':
                $link = $data[2];
                return Additional::output_file($link, 'ogolne_warunki_ubezpieczenia_ski.pdf');
                break;
            case 'zip':
                $filename = $data[2];
                $outputName = explode('/', $filename);
                return Additional::output_file($filename, end($outputName));
            default:
                $this->ErrorOutput('404','BTN');
                break;
        }
        
    }
    
    public function FlushFile($fname){
        if(file_exists($fname)){
            header_remove();
            header('Content-type: '.Additional::getMimeType($fname));
//            header("Content-Length: ".  filesize($fname));
            readfile($fname);
        }
        die();
    }
    
    public function OutputFile($fpath,$fname,$mime = ''){
        Additional::output_file($fpath, $fname,$mime);
    }
    
    private function ErrorOutput($status = '404',$msg = false){
        switch($status){
            default:
            case '404':
                header('HTTP/1.1 404 File not found');
                if($msg){
                    echo $msg;
                }
                die();
                break;
        }
    }
}
