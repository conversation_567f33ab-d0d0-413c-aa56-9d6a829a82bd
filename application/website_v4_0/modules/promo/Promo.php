<?php

use App\Models\KuponyEmisje;

class Promo extends Module
{
    public function __construct($app)
    {
        $this->app = &$app;
        $this->baza = Database::activate();
        switch ($this->app->urlparam['a']) {

            case 'get':
                $this->GetPromo();
                break;
            default:
                abort(404);
                break;
        }

    }

    protected function _checkToken()
    {
        if(!defined('QR_PROMO_USE_HASH') || QR_PROMO_USE_HASH !== 'true') {
            return true;
        }

        $token = getallheaders();
        $rawToken = $token['X-Token'];
        if(empty($rawToken)) {
            abort(403);
        }

        $source = file('allist.dat', FILE_IGNORE_NEW_LINES ^ FILE_SKIP_EMPTY_LINES);
        if(false === $source || [] === $source) {
            abort(403, 'No servers defined');
        }

        foreach ($source as $decoded) {
            if (md5($decoded) === $rawToken) {
                return true;
            }
        }

        abort(403);
    }



    public function GetPromo(): bool
    {
        $mgo = Mymongo::activate();
        $this->_checkToken();
        $this->app->SetDecorator('Ajax');
        header('Content-Type: application/json; charset=utf-8');

        /**
         * @var KuponyEmisje[] $kemissions
         */
        $kemissions = (new KuponyEmisje())->findByContractNumber($this->app->urlparam['code']);
        if (null === $kemissions || [] === $kemissions) {
            return $this->errorResponse('No promo found');
        }

        $model = new Kupon();
        $kupon = null;
        $ke = null;
        foreach ($kemissions as $emission) {
            if (!$emission->isActive()) {
                continue;
            }
            $kupon = $model->GetNextActiveCoupon($emission->id);
            if (null !== $kupon) {
                $ke = $emission;
                break;
            }
        }
        if(null === $kupon) {
            return $this->errorResponse('No promo code found');
        }

        $oferta = $this->baza->select_r('SELECT oferta FROM ' . TABLE_PREFIX . 'kupony_emisje_oferty WHERE emisja = ' . $ke->id);
        if (empty($oferta)) {
            return $this->errorResponse('No promo offer found');
        }
        $offerada = $mgo->mget_offer_details($oferta['oferta']);
        if (empty($offerada)) {
            return $this->errorResponse('No offer found');
        }
        $partner_sp = (new \App\Models\Partner())->findOne($offerada['partnerzy_sp'][0]['id']);
        if (empty($partner_sp)) {
            return $this->errorResponse('No partner found');
        }


        $resp = $this->getEmptyResponse();
        $resp['url'] = sprintf('https://%s/order?promo=%s', $partner_sp->domain, $kupon['serial']);
        $this->app->ADD('response', json_encode($resp));
        return true;
    }

    public function ListPromotions()
    {

    }

    protected function getEmptyResponse()
    {
        return [
            'success' => true,
            'error' => '',
            'url' => ''
        ];
    }

    protected function errorResponse($message): bool
    {
        $this->app->SetDecorator('Ajax');
        $resp = $this->getEmptyResponse();
        $resp['success'] = false;
        $resp['error'] = $message;
        $this->app->ADD('response', json_encode($resp));
        return true;
    }


}
