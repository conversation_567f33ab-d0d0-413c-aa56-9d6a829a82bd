<?php
/**
 * <PERSON>ody do uprawnień:
 * @@@@
 *
 *
 * @package Modules
 * @subpackage Users
 * 
 */


class Userlogin extends Module{
    
    public $app      = null;
    public $en       = null;
    public $alerts   = array();
    public $postback = array();
    public $tpl_data = null;
	
	public function __construct($app) {
            $this->app =&$app;
            $this->en = Database::activate();
            $this->app->SetDecorator('Ajax');
            
            switch( $this->app->urlparam['a'] ) 
            {
                case 'confpage'     : $this->confPage();                                                 break;
                case 'loginpage'    : $this->loginPage();                                                break;
                case 'registerpage' : $this->registerPage();                                             break;
                case 'lostpage'     : $this->lostPage();                                                 break;
                case 'login'        : $this->app->addDecoratorData( 'response', $this->Login() );        break;
                case 'logout'       : $this->app->addDecoratorData( 'response', $this->Logout() );       break;
                case 'register'     : $this->app->addDecoratorData( 'response', $this->Register() );     break;
                case 'lostpwd'      : $this->app->addDecoratorData( 'response', $this->LostPwd() );      break;
                case 'savedefaults' : $this->app->addDecoratorData( 'response', $this->SaveDefaults() ); break;
                case 'regform'      : $this->RegisterDisplay();                                          break;
                case 'reg'          : $this->Register();                                                 break;
                case 'regajax'      : $this->RegisterAjax();                                             break;
                case 'forgetpass'   : $this->ForgetPassword($_POST);                                     break;
                case 'changepass'   : $this->ChangePass($_POST);                                     break;
            }
	}
        
        public function loginPage()
        {
            $redirect = SITE_URL . 'order';
            if (ESID_USE_TRANS_SID) {
                $redirect = $this->app->appendESID($redirect);
            }
            if ( Sessions::IsLoggedIn() )
            {
                header( 'Location: ' . $redirect );
            }
            else
            {
				$lang = $this->app->Lang('Logowanie','F_Użytkownik');
                $this->tpl_data['redirect'] = $redirect;
                $this->display( 'login', $lang );
            }
        }
        
        public function registerPage()
        {
            $redirect = SITE_URL . 'order';
            if (ESID_USE_TRANS_SID) {
                $redirect = $this->app->appendESID($redirect);
            }
            if ( Sessions::IsLoggedIn() ) 
            {
                header( 'Location: ' . $redirect );
            }
            else
            {
                $lang = $this->app->Lang('Rejestracja','F_Użytkownik');
                $this->tpl_data['redirect'] = $redirect;
                $this->display( 'register', $lang );
            }
        }
        
        public function lostPage()
        {
            if ( Sessions::IsLoggedIn() ) 
            {
                header( 'Location: ' . SITE_URL . 'order' );
            }
            else
            {
				$lang = $this->app->Lang('Resetowanie hasła','F_Użytkownik');
                $this->display( 'lost', $lang );
            }
        }
        
        public function confPage()
        {
            if ( Sessions::IsLoggedIn() ) 
            {
                header( 'Location: ' . SITE_URL . 'order' );
            }
            else
            {
                $get = $this->app->urlparam;
                
                if ( $get['hash'] )
                {
                    $hash = addslashes( htmlentities( $get['hash'] ) );
                    $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'uzytkownik ';
                    $sql.= 'WHERE lostpwd = "' . $this->en->Escape( $hash ) . '" AND uprawnienia between 1 AND 55';
                    $data = $this->en->select_r( $sql );
                    
                    if ( $data['id'] > 0 )
                    {
                        $data_max = $data['lostpwdts'] + 3 * 60 * 60;
                        
                        if ( time() > $data_max )
                        {
                            $this->alerts['danger'][] = $this->app->Lang('Podany link już wygasł. Spróbuj zresetować hasło ponownie.','F_Użytkownik');
                        }
                        else
                        {
                            $new_pass = substr(md5( microtime() . $data['id'] . $data['email'] . $get['hash'] ), 0, 8);
                            $pass = md5( $new_pass . SALT );
                            $sql = 'UPDATE ' . TABLE_PREFIX . 'uzytkownik ';
                            $sql.= 'SET md5_haslo="' . $pass . '", lostpwdts="0", lostpwd="" ';
                            $sql.= 'WHERE id = ' . $data[ 'id' ];
                            $res = $this->en->query( $sql );
                            
                            require_once ENSETTINGSDIR.'libs/Mailsend.class.php';
                            Mailsend::activate();
                            $system = Tools::GetSystemSettings();

                            // Pola obowiązkowe

                            $params['user']  = $data['id']; 
                            $params['lang']  = $data['defaultlang'];
                            $params['email'] = $data['email'];

                            // Pola opcjonalne, zależne od szablonu wiadomości

                            $params[ md5( '%SITE_URL%' ) ]    = $system['siteUrl']['value'];
                            $params[ md5( '%SITE_NAME%' ) ]   = $system['siteName']['value'];
                            $params[ md5( '%SITE_EMAIL%' ) ]  = $system['bokEmail']['value'];
                            $params[ md5( '%USER_LNAME%' ) ]  = $data['nazwisko'];
                            $params[ md5( '%USER_FNAME%' ) ]  = $data['imie'];
                            $params[ md5( '%USER_EMAIL%' ) ]  = $data['email'];
                            $params[ md5( '%USER_NEWPWD%' ) ] = $new_pass;

                            if ( Mailsend::SendEmail( '18_forgot_password_confirm', $params ) )
                            {
                                $this->alerts['success'][] = $this->app->Lang('Na twój adres email zostało wysłane nowe hasło.','F_Użytkownik');
                            }
                            else
                            {
                                $this->alerts['danger'][] = $this->app->Lang('Nie udało się wysłać wiadomość z nowym hasłem.','F_Użytkownik');
                            }
                        }
                    }
                    else
                    {
                        $this->alerts['danger'][] = $this->app->Lang('Nieprawidłowy URL. Sprawdź czy adres na pewno zgadza się z odnośnikiem w wiadomości.','F_Użytkownik');
                    }
                }
                else
                {
                    $this->alerts['danger'][] = $this->app->Lang('Nieprawidłowy URL. Sprawdź czy adres na pewno zgadza się z odnośnikiem w wiadomości.','F_Użytkownik');
                }
                $lang = $this->app->Lang('Resetowanie hasła','F_Użytkownik');
                $this->display( 'conf', $lang );
            }
        }
        
        
	
        /**
         * Inicjalizacja logowania (ajax)
         * @return string JSON Encoded
         */
	protected function Login() 
        {
            $vars = $this->app->postparam;
            $resp = array();
            if ( isset( $vars['email'] , $vars['haslo'] ) ) 
            {
                $email  = addslashes( htmlentities( $vars['email'] ) );
                $passwd = $vars['haslo'];
                Sessions::Login( $email, $passwd );

                if ( Sessions::IsLoggedIn() === true ) {
                    //log
                    $this->app->LogInsert( "login", User::GetUserID(), "Login" );
                    if ( $vars['rememberme'] === 'yes' ) 
                    {
                        Sessions::RememberMe();
                    }
                    $resp["result"] = true;
                    $redirect = $_SESSION['redirect'] ?: $vars['redirect'];
                    $resp["redirect"] = urldecode( $redirect );
                    unset( $_SESSION['redirect'] );
                    if(SCONTEXT === 'front') {
                        $basket = new BasketData();
                        $basket->getBasket();
                        $basket->removeNoLoginData();
                        $basket->setBasket(false);
                    }

                }
                else 
                {
                    $resp["result"] = false;
                    $resp["error"]  = $this->app->Lang('Nieprawidłowy login lub hasło','F_Użytkownik');
                }

            } 
            else 
            {
                $resp["result"] = false;
                $resp["error"]  = $this->app->Lang('Nieprawidłowe parametry','F_Użytkownik');
            }
            return json_encode( $resp );
	}
	
        /**
         * Inicjalizacja obsługi zgubionego hasła (Ajax)
         * @return string JSON Encoded
         */
	protected function Lostpwd() 
        {
            $post = $this->app->postparam;
            $resp = array();
            
            if ( $post['email'] )
            {
                $email = addslashes( htmlentities( $post['email'] ) );
                $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'uzytkownik ';
                $sql.= 'WHERE email = "' . $this->en->Escape( $email )  . '" ';
                $sql.= 'AND uprawnienia > 0 and uprawnienia < 56';
                $data = $this->en->select_r( $sql );
                
                if ( $data['id'] > 0 )
                {
                    $hash = md5( "IAHGD*32hhf7892r3yqaikhi9" . time() . "IJAH*7d2qhhnyga76sq" . $data[ 'id' ] . $data[ 'email' ] );
                    $sql = 'UPDATE ' . TABLE_PREFIX . 'uzytkownik ';
                    $sql.= 'SET lostpwd="' . $hash . '", lostpwdts=' . time() . ' ';
                    $sql.= 'WHERE id = ' . $data[ 'id' ];
                    $res = $this->en->query( $sql );
                    require_once ENSETTINGSDIR.'libs/Mailsend.class.php';
                    Mailsend::activate();
                    $system = Tools::GetSystemSettings();

                    // Pola obowiązkowe

                    $params['user']  = $data['id']; 
                    $params['lang']  = $data['defaultlang'];
                    $params['email'] = $data['email'];

                    // Pola opcjonalne, zależne od szablonu wiadomości

                    $params[ md5( '%SITE_URL%' ) ]        = SITE_URL;
//                    $params[ md5( '%SITE_URL%' ) ]        = $system['siteUrl']['value'];
//                    $params[ md5( '%SITE_NAME%' ) ]       = $system['siteName']['value'];
                    $params[ md5( '%SITE_NAME%' ) ]       = SITE_NAME;
                    $params[ md5( '%SITE_EMAIL%' ) ]      = $system['bokEmail']['value'];
                    $params[ md5( '%USER_LNAME%' ) ]      = $data['nazwisko'];
                    $params[ md5( '%USER_FNAME%' ) ]      = $data['imie'];
                    $params[ md5( '%USER_EMAIL%' ) ]      = $data['email'];
                    $params[ md5( '%USER_CONFIRMURL%' ) ] = SITE_URL . "userlogin/confpage?hash=" . $hash;
//                    $params[ md5( '%USER_CONFIRMURL%' ) ] = $system['siteUrl']['value'] . "userlogin/confpage?hash=" . $hash;
                    $this->app->LogInsert( "forget", 0, "Password reset: " . $data[ 'email' ] );
                    
                    if ( Mailsend::SendEmail( '17_forgot_password', $params ) )
                    {
                        $this->app->LogInsert( "forget", 0, "Mail sent: " . $data[ 'email' ] );
                        $resp['result'] = true;
                        $resp['msg'] = $this->app->Lang('Na Twój adres email została wysłana wiadomość z linkiem resetującym hasło.','F_Użytkownik');
                    }
                    else
                    {
                        $resp['result'] = false;
                        $resp['msg'] = $this->app->Lang('Nie udało się wysłać wiadomości z linkiem resetującym hasło.','F_Użytkownik');
                        $this->app->LogInsert( "forget", 0, "Mail sent error: " . $data[ 'email' ] );
                    }
                }
                else
                {
                    $resp['result'] = false;
                    $resp['msg'] = $this->app->Lang('Nie znaleziono podanego adresu email.','F_Użytkownik');
                }
            }
            else
            {
                $resp['result'] = false;
                $resp['msg'] = $this->app->Lang('Podano nieprawidłowe parametry.','F_Użytkownik');
            }
            
            return json_encode( $resp );
        }
	
        /**
         * Inicjalizacji funkcji wylogowania (Ajax)
         * @return string JSON Encoded
         * @see Sessions::Logout()
         */
	protected function Logout() {
		$vars = $this->app->urlparam;		
		$resp=array();
                if($_SESSION['SCONTEXT'] === 'partner'){
                    $resp["redirect"] = SITE_URL;
                    $loc = SITE_URL;
                }
                else {
                    $resp["redirect"]=urldecode($vars['redirect']);
                    $loc = SITE_URL.'order';
                    if (defined('ESID_USE_TRANS_SID') && ESID_USE_TRANS_SID === true) {
                        $loc = $this->app->appendESID($loc);
                    }
                }
		$this->app->LogInsert("logout",  User::GetUserID(),"Logout");
		Sessions::Logout();
		$resp["result"]=true;
        if($_GET['ajax']){
            return json_encode($resp);
        }
        header('Location: '.$loc);

	}
        
        protected function Register()
        {
            $post = $this->app->postparam;
            $this->postback = $post;
            $val = $this->RegisterVal( $post );

            if ( ! $val['errors'] && ! $val['warnings'] ) 
            {
                if ( ! $this->AddNewUser( $post ) )
                {
                    $alert = $this->app->Lang('Podczas rejestracji wystąpił błąd.','F_Użytkownik');
                    $alert.= $this->app->Lang('Spróbuj się zalogować. W razie problemów spróbuj stworzyć jeszcze raz konto lub skontaktuj się z administracją.','F_Użytkownik');
                    $this->alerts['danger'][] = $alert;
                    $this->RegisterPage();
                }
                else
                {
                    require_once ENSETTINGSDIR.'libs/Mailsend.class.php';
                    Mailsend::activate();
                    $system = Tools::GetSystemSettings();
                    $params['user']  = User::getUserID(); 
                    $params['lang']  = User::getUserDefaultLang();
                    $params['email'] = User::getUserEmail();
                    $params[ md5( '%SITE_URL%' ) ]   = $system['siteUrl']['value'];
                    $params[ md5( '%SITE_EMAIL%' ) ] = $system['bokEmail']['value'];
                    Mailsend::SendEmail( '1_register', $params );
                    // $redirect = $_SESSION['redirect'] ? $_SESSION['redirect'] : SITE_URL . 'account/profile';
                    // $redirect = urldecode( $redirect );
                    if($_SESSION['payment']){
                        $payment_redirect = SITE_URL.'order/payment';
                        if (ESID_USE_TRANS_SID) {
                            $payment_redirect = $this->app->appendESID($payment_redirect);
                        }
                        header('Location: '. $payment_redirect);
                    }
                    else if(isset($_SESSION['basket']['zamowienia']) AND count($_SESSION['basket']['zamowienia'])>0){
                        $order_redirect = SITE_URL.'order';
                        if (ESID_USE_TRANS_SID) {
                            $order_redirect = $this->app->appendESID($order_redirect);
                        }

                        header('Location: '. $order_redirect);
                    }
                    else {
                        $profile_redirect = SITE_URL . 'account/profile';
                        if (ESID_USE_TRANS_SID) {
                            $profile_redirect = $this->app->appendESID($profile_redirect);
                        }
                        header( 'Location: ' . $profile_redirect );
                    }
                }
            }
            else
            {
				$lang = $this->app->Lang('Rejestracja','F_Użytkownik');
                $this->alerts['danger'][] = $val['warnings'];
                $this->display( 'register', $lang );
            }
        }
        
        protected function RegisterAjax(){
            $post = $this->app->postparam;
        }
        
        public function RegisterVal( $post )
        {
			$l_ema = $this->app->Lang('Email','F_Użytkownik');
			$l_pem = $this->app->Lang('Powtórz email','F_Użytkownik');
			$l_pha = $this->app->Lang('Powtórz hasło','F_Użytkownik');
			$l_has = $this->app->Lang('Hasło','F_Użytkownik');
			$l_are = $this->app->Lang('Akceptacja regulaminu','F_Użytkownik');
			$l_zgo = $this->app->Lang('Zgoda na przetwarzanie danych','F_Użytkownik');
            $val[] = array( $post['email'], 'REQUIRED', $l_ema );
            $val[] = array( $post['haslo'], 'REQUIRED', $l_has);
            $val[] = array( $post['email'], 'EMAIL'   , $l_ema);
            $val[] = array( $post['email'], 'EMAILUSED');
            $val[] = array( array( $post['email'], $post['powt_email'] ), 'MATCHES', array($l_ema, $l_pem) );
            $val[] = array( array( $post['haslo'], $post['powt_haslo'] ), 'MATCHES', array('Hasło', $l_pha) );
            $val[] = array( array( $post['email'], 200), 'MAX_LENGTH', $l_ema);
            $val[] = array( array( $post['haslo'], 100), 'MAX_LENGTH', $l_ema);
            $val[] = array( array( $post['haslo'], 6  ), 'MIN_LENGTH', $l_has);
            $val[] = array( array( intval( $post['regulamin'] )    , 1), 'CHECKED', $l_are);
            $val[] = array( array( intval( $post['przetwarzanie'] ), 1), 'CHECKED', $l_zgo);
            return Validation::ValForm($val);
        }
        
        public function AddNewUser( $data ) 
        {
            $passwd = md5( $data['haslo'] . SALT );
            $result = USER::Register( $data['email'], $passwd );
            if ( ! $result )
            {
                return false;
            }
            else
            {
                $login = Sessions::Login( $data['email'], $data['haslo'] );
                //add user to uzytkownik_meta (empty row)
                if($login){
                    $sql = 'INSERT INTO '. TABLE_PREFIX. 'uzytkownik_meta SET item='. User::GetUserID().', rodzaj="osobiste"';
                    $this->en->query($sql);
                } else {
                    return false;
                }
                return $login;
            }
        }

    public function ForgetPassword($post)
    {
        $resp = array();
        if ($post['forget_email'] && !(false === filter_var($post['forget_email'], FILTER_VALIDATE_EMAIL)))
        {
            $email = addslashes( htmlentities( $post['forget_email'] ) );
            $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'uzytkownik ';
            $sql.= 'WHERE email = "' . $this->en->Escape( $email )  . '" ';
            $sql.= 'AND uprawnienia > 0';
            $data = $this->en->select_r( $sql );

            if ( $data['id'] > 0 )
            {
                $hash = hash("adler32", "IAHGD*32hhf7892r3yqaikhi9" . time() . "IJAH*7d2qhhnyga76sq" . $data[ 'id' ] . $data[ 'email' ] );
//                    $hash = md5( "IAHGD*32hhf7892r3yqaikhi9" . time() . "IJAH*7d2qhhnyga76sq" . $data[ 'id' ] . $data[ 'email' ] );
                $sql = 'UPDATE ' . TABLE_PREFIX . 'uzytkownik ';
                $sql.= 'SET lostpwd="' . $hash . '", lostpwdts=' . time() . ' ';
                $sql.= 'WHERE id = ' . $data[ 'id' ];
                $res = $this->en->query( $sql );
                require_once ENSETTINGSDIR.'libs/Mailsend.class.php';
                Mailsend::activate();
                $system = Tools::GetSystemSettings();

                // Pola obowiązkowe

                $params['user']  = $data['id'];
                $params['lang']  = $data['defaultlang'];
                $params['email'] = $data['email'];

                // Pola opcjonalne, zależne od szablonu wiadomości

                $params[ md5( '%SITE_URL%' ) ]        = $system['siteUrl']['value'];
                $params[ md5( '%SITE_NAME%' ) ]       = $system['siteName']['value'];
                $params[ md5( '%SITE_EMAIL%' ) ]      = $system['bokEmail']['value'];
                $params[ md5( '%USER_LNAME%' ) ]      = $data['nazwisko'];
                $params[ md5( '%USER_FNAME%' ) ]      = $data['imie'];
                $params[ md5( '%USER_EMAIL%' ) ]      = $data['email'];
//                    $params[ md5( '%USER_CONFIRMURL%' ) ] = $system['siteUrl']['value'] . "userlogin/confpage?hash=" . $hash;
                $params[ md5( '%RESETHASH%' ) ] = $hash;
                $this->app->LogInsert( "forget", 0, "Password reset: " . $data[ 'email' ] );

                if ( Mailsend::SendEmail( '17_forgot_password', $params ) )
                {
                    $this->app->LogInsert( "forget", 0, "Mail sent: " . $data[ 'email' ] );
                    $resp['result'] = true;
                    $resp['msg'] = $this->app->Lang('Na Twój adres email została wysłana wiadomość z kodem resetującym hasło.','F_Użytkownik');
                }
                else
                {
                    $this->app->LogInsert( "forget", 0, "Mail sent error: " . $data[ 'email' ] );
                    $resp['error'] = true;
                    $resp['msg'] = $this->app->Lang('Nie udało się wysłać wiadomości z kodem resetującym hasło.','F_Użytkownik');
                }
            }
            else
            {
                $resp['result'] = true;
                $resp['msg'] = $this->app->Lang('Na podany adres email wysłano wiadomość z kodem.','F_Użytkownik');
            }
        }
        else
        {
            $resp['error'] = true;
            $resp['msg'] = $this->app->Lang('Podano nieprawidłowe parametry.','F_Użytkownik');
        }
        $this->app->ADD('response', json_encode($resp));
        return;
    }

    public function ChangePass($post)
    {
        if ($post['hash'])
        {
            $hash = substr(addslashes(htmlentities($post['hash'])), 0, 10);
            $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'uzytkownik ';
            $sql.= 'WHERE lostpwd = "' . $this->en->Escape( $hash ) . '"';
            $data = $this->en->select_r( $sql );

            if ($data['id'] > 0)
            {
                $data_max = $data['lostpwdts'] + 3 * 60 * 60;

                if (time() > $data_max)
                {
                    $resp['error'] = true;
                    $resp['errormsg'] = $this->app->Lang('Podany kod wygasł lub nie istnieje. Spróbuj zresetować hasło ponownie.','F_Użytkownik');
                    $this->app->ADD('response', json_encode($resp));
                    return;
                }
                $pass = $post['haslo'];
                $pass_confirmed = $post['haslo_confirmed'];
                $val[] = array(array($pass, 70 ), 'MAX_LENGTH', $this->app->Lang('Nowe hasło','F_Konto użytkownika'));
                $val[] = array(array($pass, 6 ),   'MIN_LENGTH', $this->app->Lang('Nowe hasło','F_Konto użytkownika'));
                $val[] = array(
                    array($pass, $pass_confirmed),
                    'MATCHES',
                    array($this->app->Lang('Nowe hasło','F_Konto użytkownika'), $this->app->Lang('Powtórz nowe hasło','F_Konto użytkownika')));
                $val = Validation::ValForm( $val );

                if ($val['errors'])
                {
                    $resp['error'] = true;
                    $resp['msg'] = Validation::makeErrorString($val['warnings']);
                    $this->app->ADD('response', json_encode($resp));
                    return;
                }

                $pass = md5($pass . SALT);
                $sql = 'UPDATE ' . TABLE_PREFIX . 'uzytkownik ';
                $sql.= 'SET md5_haslo="' . $pass . '", lostpwdts=0, lostpwd="" ';
                $sql.= 'WHERE id = ' . $data['id'];
                $res = $this->en->query( $sql );
                $this->app->LogInsert("newpass", 0, "Pass Changed" . $data['email']);

                require_once ENSETTINGSDIR.'libs/Mailsend.class.php';
                Mailsend::activate();
                $system = Tools::GetSystemSettings();

                // Pola obowiązkowe

                $params['user']  = $data['id'];
                $params['lang']  = $data['defaultlang'];
                $params['email'] = $data['email'];

                // Pola opcjonalne, zależne od szablonu wiadomości

                $params[ md5( '%SITE_URL%' ) ]    = $system['siteUrl']['value'];
                $params[ md5( '%SITE_NAME%' ) ]   = $system['siteName']['value'];
                $params[ md5( '%SITE_EMAIL%' ) ]  = $system['bokEmail']['value'];
                $params[ md5( '%USER_LNAME%' ) ]  = $data['nazwisko'];
                $params[ md5( '%USER_FNAME%' ) ]  = $data['imie'];
                $params[ md5( '%USER_EMAIL%' ) ]  = $data['email'];
//                            $params[ md5( '%USER_NEWPWD%' ) ] = $new_pass;

                if(Mailsend::SendEmail( '18_forgot_password_confirm', $params)){
                    $this->app->LogInsert("newpass", 0, "Mail sent: " . $data['email']);
                } else {
                    $this->app->LogInsert("newpass", 0, "Error Mail sent: " . $data['email']);
                }
                $resp['result'] = true;
                $resp['msg'] = $this->app->Lang('Hasło zostało zmienione.','F_Użytkownik');
                $this->app->ADD('response', json_encode($resp));
                return;
            }
            else
            {
                $resp['error'] = true;
                $resp['msg'] = $this->app->Lang('Podany kod wygasł lub nie istnieje. Spróbuj zresetować hasło ponownie.','F_Użytkownik');
                $this->app->ADD('response', json_encode($resp));
                return;
            }
        }
        else
        {
            $resp['error'] = true;
            $resp['msg'] = $this->app->Lang('Podany kod wygasł lub nie istnieje. Spróbuj zresetować hasło ponownie.','F_Użytkownik');
            $this->app->ADD('response', json_encode($resp));
            return;
        }

    }


    protected function display( $tpl, $title = 'Logowanie' )
        {
            $this->app->SetDecorator( 'Smarty' );
            $h_data['title'] = $title;
            $this->app->AddWidgetsData( 'header', $h_data );
            $this->app->AddWidgetsData( 'alerts', $this->alerts );
            $this->app->AddDecoratorData( 'data', $this->tpl_data );
            $this->app->AddDecoratorData( 'postback', $this->postback );
            $this->app->SetTemplate( 'modules/userlogin/' . $tpl . '.tpl' );
        }
	
}