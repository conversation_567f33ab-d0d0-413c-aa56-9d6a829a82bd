<?php

/** 
 * Instalator kontrolera 'Account'
 * @package Modules
 * @subpackage Account
 */

class install {
    
    public static $en='';
    public static $main='';
    public static $langs='';
    
    public static function _active(&$main,$langs) {
        self::$main = &$main;
        self::$en = &$main->en;
        self::$langs = $langs;
    }

    
public static function doinstall($param){
    echo "<pre>";
    self::$param();
//    $this->installTables();
//    $this->all_langs();
//    $this->installSP();
//    $this->installTriggers();
    
    echo "</pre>";
}


public static function addpermission() {
$tp = TABLE_PREFIX;
    $sql=<<<PHDOC
            insert {$tp}uprawnienia (rola,modul) values 
(3,'account'),
(1,'account'),
(1,'account_displayuserdetails'),
(3,'account_displayuserdetails'),
(3,'account_displaypocket'),
(3,'account_ppcancelreturn'),
(3,'account_startpayment'),
(3,'account_deletepostedinquiery'),
(1,'account_deletepostedinquiery'),
(1,'account_displaypostedinquiries'),
(3,'account_displaypostedinquiries'),
(3,'account_deletereceivedinquiery'),
(3,'account_displayreceivedinquiries');
            
PHDOC;
$sql_d = "DELETE FROM {$tp}uprawnienia WHERE modul LIKE 'account%'";
if(!self::$en->query($sql_d)) die(self::$en->errormsg);  
if(!self::$en->query($sql)) die(self::$en->errormsg);

echo <<<ERT
permission.... DONE
ERT;
}

//public static function createfolders() {
//    mkdir('data/'.TABLE_PREFIX.'service');
//    copy('data/default.jpg', 'data/'.TABLE_PREFIX.'service/default.jpg');
//    echo "Folders done... \n";
//}

public static function display(){
    echo <<<HTDOC

   <!doctype HTML>
<html>
    <head>
        <meta charset="utf8">
        
    </head> 
    <body>
        
        <form>      
            <input type='checkbox' name='permisions' value='1'> Permissions<br>           
            <input type='submit' name="submit" value='RUN'><br>          
        </form>
        
    </body>
</html>    
        
HTDOC;
    
}
}  
?>


