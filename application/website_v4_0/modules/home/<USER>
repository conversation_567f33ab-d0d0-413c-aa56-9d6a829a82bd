<?php

/**
 * Obsługa strony domowej
 * @package Application
 * @subpackage Modules
 */

class Home extends Module
{
    public $app       = null;
    public $urlpram   = null;
    public $topparams = array();

    public function __construct( $app ) 
    {
        $this->app = &$app;
        switch ( $app->urlparam['a'] )
        {            
            default: 
                $this->Home();
                break;
                
            case 'test':
                $this->Test();
                break;
        }   
    }
     
    /**
     * Generuje stronę początkową
     */
    public function Home()
    {
        $mongo = Mymongo::activate();
        $dane  = $mongo->mget_user_offers_list( array(), array() );
        if ( $dane ) {
            $dane = Userproducts::UnsetUnavailableOffers( $dane );
            foreach( $dane as &$d ) {
                $offer[] = array(
                    'id'       => $d['id'],
                    'parentid' => $d['parentid'],
                    'nazwa'    => $d['nazwa'],
                    'osrodki'  => $d['osrodki'],
                    'data_od'  => $d['data_od'],
                    'data_do'  => $d['data_do'],
                );
            }
        }
        $this->topparams['meta']['title'] = 'e-Skipass';
        $this->ProductTpl( 'home', $offer );
    }
    
    public function Test()
    {
        $m_time = new MongoDate( time() );
        $fields = array( 'parentid' => 1, 'data_od' => array( 'day' => 1 ), 'data_do' =>1 );
//        $where  = array( 'data_od' => array( '$lte' => $m_time ) );
        $offer = new OfferData();
        $offer->SetCurrentOffers();
//        $offer->Match( $where );
        $offer->Project( $fields );
        $offer->GetOffer();
        Tools::PA( $offer->output, 'Output' );
    }
    
    private function ProductTpl( $tpl, $data ) {
        $this->app->AddWidgetsData( 'topnav' );
        $this->app->AddWidgetsData( 'footer' );
        $this->app->AddWidgetsData( 'header', $this->topparams);
        $this->app->AddWidgetsData( 'accountnav' );
        $this->app->AddWidgetsData( 'basket' );
        $this->app->AddDecoratorData( 'oferta', $data );
        $this->app->SetTemplate( 'modules/home/' . $tpl . '.tpl');
    }
}