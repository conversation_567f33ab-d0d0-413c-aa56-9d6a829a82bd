<?php
/**
 * <PERSON>ody do uprawnień:
 * @@@@
 *
 * Klasa statyczna zarządzania finansami
 * @package Modules
 * @subpackage Finanse
 *
 */
class Finance {
	
	private static $stanPortfela = 0;
        private static $activ = false;
        static $en = null;
        static $FullHistoryDesc = array(
            'PROMO'=>
            'Promotion: %s. Broadcast date: %s, duration: %s. Object: %s',
            'CHARGE'=>
            'Charge wallet. Provider: %s',
        );
                
        static $PromoNames = array(
            'TOP' =>'"Guaranteed position"',
            'BOX' =>'"Offer list baner"',
            'ASKBOOKING' =>'"Ask for booking"',
            'BANNER' =>'"Home page banner"'
        );

        static $ChargeProviders = array(
            'PAYPAL' =>'"PayPal"'
        );
        
        private static $statusy= array(1=>'New',3=>'Denied',4=>'Pending',99=>'Completed');
        
        public static function __activate(){
            if(!is_null(self::$en)) return;
            self::$en = Database::activate();
            self::$activ = true;
        }
	
	/**
         * Historia płatności dla usera<br>
         * Pobranie danych i przygotowanie ich do wyświetlenia
         * @param int $user ID usera lub 0
         * @param res $en Baza danych
         * @return type
         */
        public static function GetPaymentHistory($user=0,$en=null){
            if(is_null($en)) $en = self::$en;
            if($user == 0) $user = User::GetUserID();
            $rows = $en->get_filtered_rows('platnosci','*',array(array('user',  $user,'INT')));
            if(count($rows)>0){
            foreach($rows as $k=>$v){
                $rows[$k]['amount'] = number_format(intval($v['amount']),0,'.','').' &euro;';
                $rows[$k]['paydate'] = $v['paydate'] == 0 || $v['paydate'] =='' ? 'not finished' : date('d-m-Y',$v['paydate']);
                $rows[$k]['status'] = self::$statusy[$v['status']];
                $rows[$k]['ts'] = date('d-m-Y',$v['ts']);
            }
            }
            return $rows;
        }

        /**
         * Historia płatności dla usera<br>
         * Pobranie danych i przygotowanie ich do wyświetlenia
         * @param int $user ID usera lub 0
         * @param res $en Baza danych
         * @return type
         */
        public static function GetFullPaymentHistory($user=0,$en=null){
            if(is_null($en)) self::__activate ();
            if($user == 0) $user = User::GetUserID();
            $rows = self::$en->get_filtered_rows('portfel','*',array(array('user',  $user,'INT')),'data|ASC');
            if(count($rows)>0){
                $l=1;
            foreach($rows as $k=>$v){
                $rows[$k]['dir'] = ($v['kwota']<0?'out':'in');
                $rows[$k]['lp'] = $l;
                $rows[$k]['kwota'] = $v['kwota'].' &euro;';
                $rows[$k]['data'] = date('Y-m-d',$v['data']);
                $rows[$k]['status'] = self::$statusy[$v['status']];
                list($typoperacji,$opisfull) = explode('@',$v['opis']);
                if($typoperacji=='PROMO'){
                    list($promo,$data,$duration,$module,$object) = explode('|',$opisfull);
                    $obname = self::$en->get_field_val(strtolower($module),'objectname_short',array(array('id',$object,'INT')));
                    $rows[$k]['opis'] = sprintf(self::$FullHistoryDesc['PROMO'],  self::$PromoNames[$promo],$data,$duration,$obname);
                }
                if($typoperacji=='CHARGE'){
                    $opisfull;
                    $rows[$k]['opis'] = sprintf(self::$FullHistoryDesc['CHARGE'],  self::$ChargeProviders[$opisfull]);
                }
                $l++;
            }
            }
            return $rows;
        }
        
        
        
	/**
         * Zapisuje operacje portfela
         * @param float $kwota
         * @param string $opis
         * @param int $user
         * @param object $en Handler bazy danych
         * @return boolean
         */
	public static function PortfelOperacja($kwota, $opis='', $user=0, $en=null) {
		if(is_null($en)) self::__activate ();
		if($user==0) $user = self::$en->GetUserID();
		
		if($kwota > self::PortfelStan($user, $en)) return false;
		$dane = array(
				array('user', $user, 'INT'),
				array('data', time(), 'INT'),
				array('kwota', $kwota, 'FLOAT'),
				array('opis', $opis, 'STRING')
		);
		
		$res = self::$en->insert_row(TABLE_PREFIX.'portfel', $dane);
		return true;
	}

        /**
         * Zwraca stan portfela usera
         * @param int $user
         * @param object $en Handler bazy danych
         * @return type
         */
	public static function PortfelStan($user=0,$en = null) {
		if(is_null($en)) self::__activate ();
		if($user==0) $user = User::GetUserID();
		
		$saldo = self::$en->get_field_val("portfel", "SUM(`kwota`)", array(array("user", $user, "INT")));
		return floatval($saldo);
	}

	/**
         * 
         * @param string $module Nazwa modułu
         * @param int $obiekt ID objektu
         * @param float $kwota Kwota
         * @param int $user ID użytkownika
         * @param object $en Handler bazy danych
         * @return boolean
         */
	public static function PozZapisz($module, $obiekt, $kwota, $user=0, $en=null) {
		if(is_null($en)) $en = self::$en;
		if($user==0) $user = $en->GetUserID();
	
		$dane = array(
				array('user', $user, 'INT'),
				array('item', $obiekt, 'INT'),
				array('kwota', $kwota, 'FLOAT'),
				array('module', $module, 'STRING')
		);
		
		$spr = $en->select_r("SELECT `id` 
				FROM ".TABLE_PREFIX."pozycjonowanie_config 
				WHERE `user`= ".intval($user)." AND `item`=".intval($obiekt)." AND `module`='".$en->Escape($module)."' 
				LIMIT 1");

		if($spr['id']>0) {
			$res = $en->update_rows(TABLE_PREFIX.'pozycjonowanie_config', $dane, array(array('id', $spr['id'], "INT")), 1);
		} else {
			$res = $en->insert_row(TABLE_PREFIX.'pozycjonowanie_config', $dane);
		}
		
		return $res;
	}
	

    public static function PozAktualnePozycje($modul, $data, $en=null) {
		if(is_null($en)) $en = self::$en;
		//echo "SELECT * FROM ".TABLE_PREFIX."pozycjonowanie_historia WHERE data=".$data." AND module='".$modul."' ORDER BY pozycja ASC, RAND() ASC";
		return $en->select("SELECT * FROM ".TABLE_PREFIX."pozycjonowanie_historia WHERE data=".$data." AND module='".$modul."' ORDER BY pozycja ASC, RAND() ASC");
	}
	
	########## TOPY
	private static function TopyLista($modul, $typ="top", $en=null) {
		if(is_null($en)) $en = self::$en;
		$data = date("Y-m")."-01";
		$topy = $en->select("SELECT * FROM `".TABLE_PREFIX."promo` WHERE date_start='".$data."' AND module='".$modul."' AND promo_type='".$typ."' ORDER BY position ASC");
		$return = array();
		for($i=0;$i<count($topy);$i++) {
			$return[$topy[$i]['position']] = $topy[$i]['object_id'];
		}
		return $return;
	}
	
	########## SORTOWANIE
	/**
	 * Sortuje dane według opłaconych pozycji (topy i pozycjonowanie)
	 * @param array $dane
	 * @param string $modul 
	 * @param object $en
	 * @param bool $topyon jeżeli true, topy zostaną wydzielone do podtablicy topy
	 * @param bool $topyall jeżeli true, wszystkie topy zostaną wrzucone do topów z pominięciem filtrowania
	 * @param bool $pozycjonowanieon jeżeli true, włączy się sortowanie wg pozycjonowania
	 * @param string $typ rodzaj topów - "top", "sidebar" itp
	 * @return array posortowane dane z dwoma podtablicami: regular i topy
	 */
	public static function Sortowanie($dane, $modul, $en=null, $topyon=false, $topyall=false, $pozycjonowanieon=false, $typ="top") {
		if(is_null($en)) $en = self::$en;
		$return = array(
				'topy'=>array(),
				'regular'=>array()
				);
		$klucze = array();
		if(count($dane)>0) {
			foreach($dane as $k=>$w) {
				$klucze[$w['id']] = $k;
			}
		}
		
		
		
		//topy
		if($topyon) {
			$topy = self::TopyLista($modul, $typ, $en);
			if(count($topy)>0 && is_array($topy)) {
				foreach($topy as $k=>$w) {
					if($w>0 && !$topyall && isset($klucze[$w])) {
						$return['topy'][]=$dane[$klucze[$w]];
						unset($dane[$klucze[$w]]);
					} elseif($w>0 && $topyall) {
						$danetop = $en->select_r("SELECT * FROM ".TABLE_PREFIX."accommodation_".LANG." WHERE id=".$w);
						if($danetop['id']>0) $return['topy'][]=$danetop;
					}
				}
			}
		}
		
		//pozycjonowanie
		if($pozycjonowanieon) {
			$maxdate = $en->get_field_val('pozycjonowanie_historia', 'MAX(data)', array(array("module", $modul)));
			//var_dump($maxdate);
			if(intval($maxdate)==0) $maxdate = time();
			$pozhistory = self::PozAktualnePozycje($modul, $maxdate, $en);
			
			if(count($pozhistory)>0) {
				foreach($pozhistory as $k=>$w) {
					if(isset($klucze[$w['item']])) {
						$return['regular'][]=$dane[$klucze[$w['item']]];
						unset($dane[$klucze[$w['item']]]);
					}
				}
			}
			
			shuffle($dane);
		}
		
		if(count($dane)>0) {
			foreach($dane as $k=>$w) {
				$return['regular'][]=$w;
			}
		}
		
		return $return;
	}

    /**
     * Pobiera szczegóły voucherów z atrakcjami dla raportu
     * @param int $dataod Data początkowa
     * @param int $datado Data końcowa
     * @param int $partner_id ID partnera
     * @return array
     */
    public static function GetVouchersWithAttractions($dataod, $datado, $partner_id, $en = null) {
        if(is_null($en)) self::__activate();
        
        // Pobieramy vouchery z atrakcjami z istniejących tabel
        $sql = "SELECT 
                o.id as order_id,
                o.data_zamowienia,
                v.nazwa,
                v.identyfikator,
                v.wariant_cenowy,
                v.attraction_base_promo_value,
                v.attractions,
                v.cena_base as elka_cena,
                o.status
            FROM ".TABLE_PREFIX."orders o
            JOIN ".TABLE_PREFIX."vouchers v ON o.id = v.order_id
            WHERE o.data_zamowienia BETWEEN ? AND ?
            AND o.partner_id = ?
            AND o.status != 'anulowane'
            AND v.attractions IS NOT NULL
            ORDER BY o.data_zamowienia DESC";

        $params = array(
            array($dataod, 'INT'),
            array($datado, 'INT'),
            array($partner_id, 'INT')
        );

        $rows = self::$en->query($sql, $params);
        
        // Formatujemy dane do struktury raportu
        $vouchery = array();
        foreach($rows as $row) {
            $voucher_id = $row['identyfikator'];
            
            if(!isset($vouchery[$voucher_id])) {
                $vouchery[$voucher_id] = array(
                    'nazwa' => $row['nazwa'],
                    'elka' => array(
                        'ilosc' => 1, // Każdy voucher ma podstawową Elkę
                        'brutto' => $row['elka_cena'],
                        'vat' => 23, // Standardowy VAT
                        'brutto_po_rabacie' => $row['elka_cena'] * (1 - ($row['attraction_base_promo_value']/100))
                    ),
                    'attractions' => array(),
                    'total' => array(
                        'ilosc' => 1,
                        'brutto' => $row['elka_cena'],
                        'brutto_po_rabacie' => 0
                    )
                );

                // Dekodujemy atrakcje z JSON
                $attractions = json_decode($row['attractions'], true);
                if($attractions) {
                    foreach($attractions as $attraction) {
                        $vouchery[$voucher_id]['attractions'][] = array(
                            'name' => $attraction['name'],
                            'ilosc' => 1,
                            'brutto' => $attraction['price']['gross'],
                            'vat' => $attraction['price']['vat'],
                            'brutto_po_rabacie' => $attraction['price']['gross'] * (1 - ($attraction['promo_value']/100)),
                            'promo_value' => $attraction['promo_value']
                        );

                        // Aktualizujemy totale
                        $vouchery[$voucher_id]['total']['brutto'] += $attraction['price']['gross'];
                        $vouchery[$voucher_id]['total']['brutto_po_rabacie'] += 
                            $attraction['price']['gross'] * (1 - ($attraction['promo_value']/100));
                        $vouchery[$voucher_id]['total']['ilosc']++;
                    }
                }

                // Dodajemy rabat Elki do totala
                $vouchery[$voucher_id]['total']['brutto_po_rabacie'] += 
                    $row['elka_cena'] * (1 - ($row['attraction_base_promo_value']/100));
            }
        }

        return array_values($vouchery);
    }
}