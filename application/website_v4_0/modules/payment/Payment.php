<?php
ini_set('max_execution_time', 300);
include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');

use MongoDB\BSON\ObjectId as MongoId;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class Payment extends Module
{
    public $app = null;
    public $urlparam = null;
    public $topparams = array();
    public $zamowienie = null;
    public $payload = null;

    public function __construct($app)
    {
        $this->app = &$app;
        $this->baza = Database::activate();
        switch ($this->app->urlparam['a']) {

            case 'notify':
                $this->app->SetDecorator('DirectOut');
                $this->Notify();
                break;
        }
    }

    protected function Notify()
    {
        $mdb = Mymongo::activate();
        $this->payload = file_get_contents('php://input');
        file_put_contents('payment_payload.dat', $this->payload);
        $type = $this->app->urlparam['type'];
        $post = $this->app->postparam;
        if (strtoupper($type) === 'P24') {
            $test = json_decode($this->payload, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $post = $test;
            }
        }
        $payment_log = array(
            'payload' => $this->payload,
            'post' => $post,
            'get' => $this->app->urlparam,
            'data' => array(
                'ts' => date('Y-m-d H:i:s', time()),
            )
        );

        $mdb->saveDocument($payment_log, 'paymentlog');

        switch (strtoupper($type)) {
            case 'DOTPAY':
                try {
                    $response = $this->app->postparam;
                    $payment = new SetPayment();
                    $payment->getPaymentData($response['control']);
                    $payment->addSystem();
                    $payment->checkResponse($response, true);

                    if (!$payment->response_checked) {
                        throw new Exception('Check response error');
                    }

                    if ($payment->dane_platnosci['status'] != 1) {
                        throw new Exception('Payment status error: ' . $payment->dane_platnosci['status']);
                    }

                    $payment->dane_platnosci['payment_id'] = $response['operation_number'];

                    $payment->finishPaymentSystem();

                    // Wysyłanie maila z powiadomieniem
                    $this->setSystemLang($response['control'], $mdb);
                    if ($payment->zamowienie['faktura'] == 1 and PROCESS_ORDER === 'sync') {
//                        Faktury::getOrderInvoice($payment->dane_platnosci['item'], false, true, 'F', true);
                        \App\Services\FakturowniaService::GetOrderInvoice($payment->dane_platnosci['item'], $this->app, true);
                    }

                    $this->sendEmailAfterPayment($payment->dane_platnosci['item']);


                } catch (Exception $e) {
                    $data = [];
                    $data[] = array('opis', 'Błąd: ' . $e->getMessage(), 'STRING');
                    $data[] = array('user', 0, 'INT');
                    $data[] = array('rid', 0, 'INT');
                    $data[] = array('rodzaj', 'payment_notify', 'STRING');
                    $data[] = array('data', date('Y-m-d H:i:s'), 'STRING');
                    $this->baza->insert_row('log', $data);
                }
                echo 'OK';
                break;
            case 'P24':
                $data = [];
                $data[] = array('opis', 'Payment notify P24.', 'STRING');
                $data[] = array('user', 0, 'INT');
                $data[] = array('rid', 0, 'INT');
                $data[] = array('rodzaj', 'payment_notify', 'STRING');
                $data[] = array('data', date('Y-m-d H:i:s'), 'STRING');
                $this->baza->insert_row('log', $data);
                try {
                    $response = json_decode($this->payload, true);
                    $payment = new SetPayment();
                    $payment->getPaymentData($response['sessionId']);
                    $payment->addSystem();
                    $payment->checkResponse($response, true);

                    if ($payment->response_checked) {
                        if ($payment->dane_platnosci['status'] == 1) {

                            $payment->dane_platnosci['payment_id'] = $response['statement'];
                            $payment->finishPaymentSystem();


                            // Wysyłanie maila z powiadomieniem

                            $this->setSystemLang($response['sessionId'], $mdb);
                            if ($payment->zamowienie['faktura'] == 1) {
//                                Faktury::getOrderInvoice($payment->dane_platnosci['item'], false, true, 'F', true);
                                \App\Services\FakturowniaService::GetOrderInvoice($payment->dane_platnosci['item'], $this->app, true);
                            }
                            $this->sendEmailAfterPayment($payment->dane_platnosci['item']);
//                            Zamowienie::SendMailAfterPayment($this->app, $payment->dane_platnosci['item']);

                        } else {
                            throw new Exception('Payment status error: ' . $payment->dane_platnosci['status']);
                        }
                    } else {
                        throw new Exception('Check response error: ' . $payment->system->error);
                    }

                } catch (Exception $e) {
                    $data = [];
                    $data[] = array('opis', 'Błąd: ' . $e->getMessage(), 'STRING');
                    $data[] = array('user', $payment->dane_platnosci['user'] ?: 0, 'INT');
                    $data[] = array('rid', $payment->dane_platnosci['id'] ?: 0, 'INT');
                    $data[] = array('rodzaj', 'payment_notify', 'STRING');
                    $data[] = array('data', date('Y-m-d H:i:s'), 'STRING');
                    $this->baza->insert_row('log', $data);
                }
                echo 'OK';
                break;


                break;
        }

    }


    public function sendEmailAfterPayment($orderid)
    {
        if (PROCESS_ORDER == 'sync') {
            Zamowienie::SendMailAfterPayment($this->app, $orderid);
        } else {
            $item = new \App\Models\ProcessOrderDelay();
            $item->id_zamowienia = $orderid;
            $item->action = 'email';
            $item->p_type = 'order';
            $item->data = json_encode(['id' => $orderid]);
            $item->save();
        }
    }

    public function GetProductFiles(&$files, $payment)
    {
        $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'vouchery WHERE pdf != "" AND id_zamowienia = ' . $payment->dane_platnosci['item'];
        $resp = $this->baza->query($sql);
        while ($row = $this->baza->fetch_row()) {
            $files[] = array('plik' => $row['pdf'], 'nazwa' => 'Voucher_' . $row['id'] . '.pdf');
        }
    }

    public function setSystemLang($orderId, $mdb)
    {
        $id_mongo = new MongoId($orderId);
        $order = $mdb->db->orders->findOne(['_id' => $id_mongo]);
        $order = $mdb->Mgo2array($order);
        $offer = $mdb->db->offers->findOne(['parentid' => $order['zamowienia'][0]['parentid'], 'current' => 1]);
        $offer = $mdb->Mgo2array($offer);
        $this->app->SetCurrency($offer['currency'], true, 1);
        $this->app->setTranslations($offer['lang']);
        $this->app->SetLang($offer['lang']);
        define('LANG', $_SESSION['LANG']);
    }
}