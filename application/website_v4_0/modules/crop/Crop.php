<?php

/**
 * Obsługa stron tekstowych
 * @package Application
 * @subpackage Modules
 */
class Crop extends Module
{
    public $app = null;
    public $urlpram = null;
    public $topparams = array();
    protected $en = null;

    private $src;
    private $data;
    private $dst;
    private $type;
    private $extension;
    private $msg;

    public function __construct($app)
    {
        $this->app = &$app;
        $this->en = Database::activate();

        switch ($app->urlparam['a']) {
            case 'gallery':
                $this->ProcessGalleryImage($_POST['avatar-img'], $_POST['avatar-opis']);
                break;
            default:
                $this->setSrc(isset($_POST['avatar_src']) ? $_POST['avatar_src'] : null);
                $this->setData(isset($_POST['avatar_data']) ? $_POST['avatar_data'] : null);
                $this->setFile(isset($_FILES['avatar_file']) ? $_FILES['avatar_file'] : null);
                $this->crop($this->src, $this->dst, $this->data, $_POST['avatar-opis']);

        }

    }


    private function setSrc($src)
    {
        if (!empty($src)) {
            $type = exif_imagetype($src);

            if ($type) {
                $this->src = $src;
                $this->type = $type;
                $this->extension = image_type_to_extension($type);
                $this->setDst();
            }
        }
    }

    private function setData($data)
    {
        if (!empty($data)) {
            $this->data = json_decode(stripslashes($data));
        }
    }

    private function setFile($file)
    {
        $errorCode = $file['error'];

        if ($errorCode === UPLOAD_ERR_OK) {
            $type = exif_imagetype($file['tmp_name']);

            if ($type) {
                $extension = image_type_to_extension($type);
                $dir = 'data/tmp_img/' . date('Ymd');
                Tools::prepareDir($dir);
//                if (!file_exists('data/tmp_img/' . date('Ymd'))) {
//                    mkdir('data/tmp_img/' . date('Ymd'));
//                }
                $src = $dir . '/' . date('YmdHis') . '.original' . $extension;

                if ($type == IMAGETYPE_GIF || $type == IMAGETYPE_JPEG || $type == IMAGETYPE_PNG) {

                    if (file_exists($src)) {
                        unlink($src);
                    }

                    $result = move_uploaded_file($file['tmp_name'], $src);

                    if ($result) {
                        $this->src = $src;
                        $this->type = $type;
                        $this->extension = $extension;
                        $this->setDst();
                    } else {
                        $this->msg = 'Failed to save file';
                    }
                } else {
                    $this->msg = 'Please upload image with the following types: JPG, PNG, GIF';
                }
            } else {
                $this->msg = 'Please upload image file';
            }
        } else {
            $this->msg = $this->codeToMessage($errorCode);
        }
    }

    private function setDst()
    {
        $dir = 'data/tmp_img/' . date('Ymd');
        Tools::prepareDir($dir);
//        if (!file_exists('data/tmp_img/' . date('Ymd'))) {
//            mkdir('data/tmp_img/' . date('Ymd'), 0775, true);
//        }
        $this->dst = $dir . '/' . md5(date('YmdHis') . rand(0, 1000000) . microtime()) . '.png';
    }

    private function crop($src, $dst, $data, $opis)
    {
        if (!empty($src) && !empty($dst) && !empty($data)) {
            switch ($this->type) {
                case IMAGETYPE_GIF:
                    $src_img = imagecreatefromgif($src);
                    break;

                case IMAGETYPE_JPEG:
                    $src_img = imagecreatefromjpeg($src);
                    break;

                case IMAGETYPE_PNG:
                    $src_img = imagecreatefrompng($src);
                    break;
            }

            if (!$src_img) {
                $this->msg = "Failed to read the image file";
                return;
            }

            $size = getimagesize($src);

            $size_w = $size[0]; // natural width
            $size_h = $size[1]; // natural height

            $src_img_w = $size_w;
            $src_img_h = $size_h;

            $degrees = $data->rotate;

            // Rotate the source image
            if (is_numeric($degrees) && $degrees != 0) {
                // PHP's degrees is opposite to CSS's degrees
                $new_img = imagerotate($src_img, -$degrees, imagecolorallocatealpha($src_img, 0, 0, 0, 127));

                imagedestroy($src_img);
                $src_img = $new_img;

                $deg = abs($degrees) % 180;
                $arc = ($deg > 90 ? (180 - $deg) : $deg) * M_PI / 180;

                $src_img_w = $size_w * cos($arc) + $size_h * sin($arc);
                $src_img_h = $size_w * sin($arc) + $size_h * cos($arc);

                // Fix rotated image miss 1px issue when degrees < 0
                $src_img_w -= 1;
                $src_img_h -= 1;
            }

            $tmp_img_w = $data->width;
            $tmp_img_h = $data->height;
            $dst_img_w = 1000;
            $dst_img_h = 645;

            $src_x = $data->x;
            $src_y = $data->y;

            if ($src_x <= -$tmp_img_w || $src_x > $src_img_w) {
                $src_x = $src_w = $dst_x = $dst_w = 0;
            } else {
                if ($src_x <= 0) {
                    $dst_x = -$src_x;
                    $src_x = 0;
                    $src_w = $dst_w = min($src_img_w, $tmp_img_w + $src_x);
                } else {
                    if ($src_x <= $src_img_w) {
                        $dst_x = 0;
                        $src_w = $dst_w = min($tmp_img_w, $src_img_w - $src_x);
                    }
                }
            }

            if ($src_w <= 0 || $src_y <= -$tmp_img_h || $src_y > $src_img_h) {
                $src_y = $src_h = $dst_y = $dst_h = 0;
            } else {
                if ($src_y <= 0) {
                    $dst_y = -$src_y;
                    $src_y = 0;
                    $src_h = $dst_h = min($src_img_h, $tmp_img_h + $src_y);
                } else {
                    if ($src_y <= $src_img_h) {
                        $dst_y = 0;
                        $src_h = $dst_h = min($tmp_img_h, $src_img_h - $src_y);
                    }
                }
            }

            // Scale to destination position and size
            $ratio = $tmp_img_w / $dst_img_w;
            $dst_x /= $ratio;
            $dst_y /= $ratio;
            $dst_w /= $ratio;
            $dst_h /= $ratio;

            $dst_img = imagecreatetruecolor($dst_img_w, $dst_img_h);

            // Add transparent background to destination image
            imagefill($dst_img, 0, 0, imagecolorallocatealpha($dst_img, 0, 0, 0, 127));
            imagesavealpha($dst_img, true);

            $result = imagecopyresampled($dst_img, $src_img, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h);

            if ($result) {
                if (!imagepng($dst_img, $dst)) {
                    $this->msg = "Failed to save the cropped image file";
                } else {
                    $this->AddFieldsAndTxt($dst, $opis);
                }
            } else {
                $this->msg = "Failed to crop the image file";
            }

            imagedestroy($src_img);
            imagedestroy($dst_img);
        }


        $response = array(
            'state' => 200,
            'message' => $this->getMsg(),
            'result' => $this->getResult()
        );

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    private function AddFieldsAndTxt($image, $text, $newwidth_dest = 0, $newheight_dest = 0)
    {
        list($width, $height, $type) = getimagesize($image);

        if ($newwidth_dest > 0 && $newheight_dest > 0) {
            $newheight = round($newwidth_dest * $height / $width);
            $newwidth = round($width * $newheight_dest / $height);
        } else {
            $newheight = $height;
            $newwidth = $width;
            $newheight_dest = $height;
            $newwidth_dest = $width;
        }
        $thumbname = $image;

        //sprawdzamy, czy obrazek jest zbyt wysoki
        if ($newheight > $newheight_dest) {
            $top = round(($newheight - $newheight_dest) / 2);
            $newwidth = $newwidth_dest;
        } else {
            $top = 0;
        }

        //czy nie za szeroki
        if ($newwidth > $newwidth_dest) {
            $left = round(($newwidth - $newwidth_dest) / 2);
            $newheight = round($newwidth * $height / $width);
        } else {
            $left = 0;
        }

        //obrazek docelowy
        $thumb = imagecreatetruecolor($newwidth_dest, $newheight_dest);
        $background = imagecolorallocate($thumb, 255, 255, 255);
        imagefill($thumb, 0, 0, $background);
        //obrazek temp
        $thumb2 = imagecreatetruecolor($newwidth, $newheight);
        $background = imagecolorallocate($thumb2, 255, 255, 255);
        imagefill($thumb2, 0, 0, $background);

        $fileType = $this->FileExt($thumbname);
        switch ($fileType) {
            case('gif'):
                $source = imagecreatefromgif($image);
                break;
            case('png'):
                $source = imagecreatefrompng($image);
                break;
            default:
                $source = imagecreatefromjpeg($image);
        }

        //tymczasowa miniaturka przed wycieciem srodka
        imagecopyresampled($thumb2, $source, 0, 0, 0, 0, $newwidth, $newheight, $width, $height);
        //wycinamy srodek
        imagecopy($thumb, $thumb2, 0, 0, $left, $top, $newwidth_dest, $newheight_dest);

        $white = imagecolorallocatealpha($thumb, 255, 255, 255, 0);
        //pole na numer klienta
        //imagefilledrectangle($thumb, round($newwidth_dest*0.05), round($newheight_dest*0.88), round($newwidth_dest*0.45), round($newheight_dest*0.96), $white);
        //pole na numer karnetu
        imagefilledrectangle($thumb, round($newwidth_dest * 0.54), round($newheight_dest * 0.88), round($newwidth_dest * 0.99), round($newheight_dest * 0.96),
            $white);

        if ($text != "") {
            $white = imagecolorallocatealpha($thumb, 255, 255, 255, 60);
            //pole na napis klienta
            imagefilledrectangle($thumb, round($newwidth_dest * 0.05), round($newheight_dest * 0.05), round($newwidth_dest * 0.95),
                round($newheight_dest * 0.18), $white);

            //dodajemy napis
            $im = imagecreatetruecolor($newwidth_dest, $newheight_dest);
            $posdest = round((30 * $newwidth_dest) / 350);
            $black = imagecolorallocate($thumb, 0, 0, 0);
            imagettftext($thumb, round((14 * $newwidth_dest) / 350), 0, $posdest, round($newheight_dest * 0.14), $black, 'data/fonts/PFAmateur.ttf', $text);
        }


        //zapis na dysku
        switch ($fileType) {
            case('gif'):
                imagegif($thumb, $thumbname);
                break;
            case('png'):
                imagepng($thumb, $thumbname, 9);
                break;
            default:
                imagejpeg($thumb, $thumbname, 95);
        }
        return true;
    }

    private function ProcessGalleryImage($img, $opis)
    {
        $dir = 'data/tmp_img/' . date('Ymd');
        Tools::prepareDir($dir);
//        if (!file_exists('data/tmp_img/' . date('Ymd'))) {
//            mkdir('data/tmp_img/' . date('Ymd'), 0775, true);
//        }
        $this->dst = $dir . '/' . md5(date('YmdHis') . rand(0, 1000000) . microtime()) . '.' . $this->FileExt($img);

        copy($img, $this->dst);

        $this->AddFieldsAndTxt($this->dst, $opis, 1000, 645);

        $response = array(
            'state' => 200,
            'message' => "",
            'result' => true,
            'img' => $this->dst,
            'opis' => $opis
        );

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    private function FileExt($filename)
    {
        $pi = pathinfo($filename);
        return strtolower($pi['extension']);
    }

    private function codeToMessage($code)
    {
        $errors = array(
            UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form',
            UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension',
        );

        if (array_key_exists($code, $errors)) {
            return $errors[$code];
        }

        return 'Unknown upload error';
    }

    public function getResult()
    {
        return !empty($this->data) ? $this->dst : $this->src;
    }

    public function getMsg()
    {
        return $this->msg;
    }
}