<?php

/**
 * Obsługa menu konta usera
 * @package Application
 * @subpackage Pager
 */
class Pager {

    private $baza = null;
    private $app = null;
    private $config = array();
    public $output = null;

    public function __construct($app, $params = array()) {
        if (!is_array($params))
            $params = array();
        $this->baza = Database::activate();
        $this->app = &$app;
        $params = $this->mkconfig($params);
        $this->DisplayPager($params);
    }

    private function mkconfig($params) {

        return array_merge($this->config, $params);
    }

    private function DisplayPager($params) {
        $return = array(
            'template' => "widgets/pager.tpl",
            'data' => $params
        );
        $this->output = $return;
    }

}
