<?php

/**
 * Obsługa górnej belki www partnera sprzedażowego
 * @package Application
 * @subpackage Widgets
 */
class Ptopnav 
{

    private $sdb    = null;
    private $app    = null;
    private $config = array();
    public  $output = null;

    public function __construct( $app, $params = array() )
    {
        if ( ! is_array( $params ) )
        {
            $params = array();
        }
            
        $this->sdb = Database::activate();
        $this->app = &$app;
        $params = $this->mkconfig( $params );
        
        $params['time']  = date('Y-m-d H:i:s');
        $params['tasks'] = Sms::ListNotes( USER::GetUserID(), 10 );
        
        $this->DisplayHeader( $params );
    }

    private function mkconfig( $params )
    {
        return array_merge( $this->config, $params );
    }

    private function DisplayHeader( $params ) 
    {
        $return = array(
            'template' => "widgets/ptopnav.tpl",
            'data'     => $params
        );
        
        $this->output = $return;
    }

}
