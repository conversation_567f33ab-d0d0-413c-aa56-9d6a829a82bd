<?php

/**
 * Obsługa menu na stronie
 * @package Application
 * @subpackage Widgets
 */
class Topnav {

    private $baza = null;
    private $app = null;
    private $config = array(
        'selected' => MODULE,
        'langselected' => LANG,
        'langs' => array(),
        'currencies' => array(),
        'currency' => array(),
        'currentlang' => array(),
        'isloggedin' => false,
        'username' => '',
        'urlredirect' => ''
    );
    public $output = null;

    public function __construct($app, $params = array()) {
        if (!is_array($params))
            $params = array();
        $this->baza = Database::activate();
        $this->app = &$app;
        
        $params = $this->mkconfig($params);
        $this->DisplayTopnav($params);
    }

    private function mkconfig($params) {
        $this->config['langs'] = $this->app->langs;
        $this->config['currencies'] = $this->app->currencies;
        $this->config['currency'] = $this->app->currency;
        $this->config['currentlang'] = $this->app->GetCurrentLang();
        $this->config['isloggedin'] = User::LoggedIn();
        $this->config['username'] = User::GetUserName();
        $this->config['useremail'] = User::GetUserEmail();
        $this->config['urlredirect'] = $this->app->redirect;
//        die(var_dump($this->config));
        return array_merge($this->config, $params);
    }

    private function DisplayTopnav($params) {

        $return = array(
            'template' => "widgets/topnav.tpl",
            'data' => $params
        );
        $this->output = $return;
    }

}
