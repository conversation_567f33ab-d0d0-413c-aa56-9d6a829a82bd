<?php

/**
 * Obsługa nagłowka strony www
 * @package Application
 * @subpackage Widgets
 */
class Header {

    private $baza = null;
    private $app = null;
    private $config = array(
        'title' => SITE_NAME,
        'ifga'=>false,
        'gaid'=>false,
        'gacommands'=>array(),
        'iffbpx' => false,
        'fbpxid'=>false,
        'fbpxcommands'=>array()
    );
    public $output = null;

    public function __construct($app, $params = array()) {
        if (!is_array($params))
            $params = array();
        $this->baza = Database::activate();
        $this->app = &$app;

        $ss = $this->app->GetSystemSettings();
        if($ss['ifga'] == 1){
            if($ss['gaid'] AND $ss['gaid'] != ''){
                $partner['gacode'] = "ga('create', '".$ss['gaid']."', 'auto','partnerTracker');\n";
                if($ss['gacommands']!=''){
                    $commands = explode(',', $ss['gacommands']);
                    foreach($commands as $com){
                        $partner['gacommands'] .="ga('partnerTracker.send', '".trim($com)."');\n";
                    }
                }
                else{
                    $partner['gacommands'] .="ga('partnerTracker.send', 'pageview');\n";
                }
            }
            $params['gapartner'] = $partner;
        }

        if($ss['iffbpx'] == 1){
            if($ss['fbpxid'] AND $ss['fbpxid'] != ''){
                $partner = array();
                if($ss['fbpxcommands']!=''){
                    $commands = explode(',', $ss['fbpxcommands']);
                    $partner['fbpxcommands'] = '';
                    foreach($commands as $com){
                        $partner['fbpxcommands'] .="fbq('init', '". $ss['fbpxid'] ."');\n";
                    }
                }
                else{
                    $partner['fbpxcommands'] ="fbq('init', '".$ss['fbpxid']."');\n";
                }
                $params['fbpartner'] = $partner;
            }
        }


        if($ss['ifcustomscript'] == 1 && $ss['customscript'] !== ''){

            $params['customscript'] = $ss['customscript'];
        }

        $params = $this->mkconfig($params);
        $params['less'] = '<link rel="stylesheet" type="text/css" href="/'.COMMONDIR.'assets/css/shops/'.$params['offerid'].'.css" />';
        $this->DisplayHeader($params);
    }

    private function mkconfig($params) {

        return array_merge($this->config, $params);
    }

    private function DisplayHeader($params) {
        $return = array(
            'template' => "widgets/header.tpl",
            'data' => $params
        );

        $this->output = $return;
    }

}
