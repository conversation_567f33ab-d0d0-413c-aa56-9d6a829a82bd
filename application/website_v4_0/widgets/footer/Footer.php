<?php

/**
 * Obsługa footera na stronie
 * @package Application
 * @subpackage Widgets
 */
class Footer {

    private $baza = null;
    private $app = null;
    private $config = array();
    public $output = null;

    public function __construct($app, $params = array()) {
        if (!is_array($params))
            $params = array();
        $this->baza = Database::activate();
        $this->app = &$app;
        $params = $this->mkconfig($params);
        $this->DisplayFooter($params);
    }

    private function mkconfig($params) {

        return array_merge($this->config, $params);
    }

    private function DisplayFooter($params) {
	//partnerzy
	$exclude = array(52);
	$partners = $this->baza->select("SELECT * "
		. "FROM `".TABLE_PREFIX."partners` "
		. "WHERE `partnertype`=6 AND `active`=1 AND `domain` LIKE '%.e-skipass.pl%' AND `id` NOT IN(".implode(",", $exclude).") "
		. "ORDER BY `name` ASC");
	
	$params['partners'] = $partners;
	
        $return = array(
            'template' => 'widgets/footer.tpl',
            'data' => $params
        );

        $this->output = $return;
    }

}
