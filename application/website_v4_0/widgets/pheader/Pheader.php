<?php

/**
 * Obsługa nagłowka strony www partnera sprzedażowego
 * @package Application
 * @subpackage Widgets
 */
class Pheader 
{

    private $sdb    = null;
    private $app    = null;
    private $config = array();
    public  $output = null;

    public function __construct( $app, $params = array() )
    {
        if ( ! is_array( $params ) )
        {
            $params = array();
        }
            
        $this->sdb = Database::activate();
        $this->app = &$app;
        $params = $this->mkconfig( $params );
        $this->DisplayHeader( $params );
    }

    private function mkconfig( $params )
    {
        return array_merge( $this->config, $params );
    }

    private function DisplayHeader( $params ) 
    {
        $return = array(
            'template' => "widgets/pheader.tpl",
            'data'     => $params
        );
        
        $this->output = $return;
    }

}
