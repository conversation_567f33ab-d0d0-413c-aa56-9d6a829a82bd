<?php

/**
 * Wyświetlanie alertów
 * @package Application
 * @subpackage Widgets
 */
class Alerts {

    private $config = array();
    private $baza   = null;
    private $app    = null;
    public  $output = null;

    public function __construct( $app, $params = array() ) {
        
        if ( ! is_array( $params ) ) {
            $params = array();
        } else {
            
            //  Ustalanie kluczy w tablicy a alertami, klucz stanowi klasę z alertów bootstrap
            
            $alert_class = array( 'info', 'danger', 'success', 'warning' );
            $alerts = $params;
            foreach ( $alerts as $key => $alert ) {
                if ( ! in_array( $key, $alert_class ) ) {
                    $alerts['danger'][] = $alert;
                    unset( $alerts[ $key ] ); 
                }
            }
            $params = array();
            $params['alerts'] = $alerts;
        }
        $this->baza = Database::activate();
        $this->app  = &$app;
        $params = $this->mkconfig( $params );
        $this->Display( $params );
    }

    private function mkconfig( $params ) {
        return array_merge( $this->config, $params );
    }

    private function Display( $params ) {
        $return = array(
            'template' => "widgets/alerts.tpl",
            'data' => $params
        );
        $this->output = $return;
    }

}
