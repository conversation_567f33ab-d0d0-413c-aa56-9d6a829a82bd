<?php

/**
 * Obsługa footera na stronie partnera sprzedażowego
 * @package Application
 * @subpackage Widgets
 */
class Pfooter
{

    private $sdb    = null;
    private $app    = null;
    private $config = array();
    public  $output = null;

    public function __construct( $app, $params = array() ) 
    {
        if ( ! is_array( $params ) )
        {
            $params = array();
        }
        $this->sdb = Database::activate();
        $this->app = &$app;
        $params = $this->mkconfig( $params );
        $this->DisplayFooter( $params );
    }

    private function mkconfig( $params )
    {
        return array_merge( $this->config, $params );
    }

    private function DisplayFooter( $params ) 
    {
        $return = array(
            'template' => 'widgets/pfooter.tpl',
            'data'     => $params
        );

        $this->output = $return;
    }

}
