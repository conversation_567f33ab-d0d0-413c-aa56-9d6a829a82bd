<?php

/**
 * Obsługa koszyka na stronie
 * @package Application
 * @subpackage Widgets
 */
class Basket {

    private $app     = null;
    private $config  = array();
    private $basket  = null;
    public  $output  = null;

    public function __construct( $app, $params = array() ) 
    {
        if ( ! is_array( $params ) )
        {
            $params = array();
        }
        
        $this->app    = &$app;
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $this->basket->getWidgetData();
        $params['widget'] = $this->basket->widget;
        
        $params = $this->mkconfig( $params );
        $this->DisplayBreadcrumbs( $params );
    }

    private function mkconfig ( $params ) 
    {
        return array_merge( $this->config, $params );
    }

    private function DisplayBreadcrumbs( $params ) 
    {
        $return = array(
            'template' => "widgets/basket.tpl",
            'data'     => $params
        );
        $this->output = $return;
    }

}
