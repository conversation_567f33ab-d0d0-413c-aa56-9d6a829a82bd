<?php

/**
 * Obsługa koszyka na stronie
 * @package Application
 * @subpackage Widgets
 */
class Pbasket {

    private $app     = null;
    private $config  = array();
    public  $output  = null;

    public function __construct( $app, $params = array() ) 
    {
        if ( ! is_array( $params ) )
        {
            $params = array();
        }
        
        $this->app    = &$app;
        $basket = new BasketData();
        $basket->getBasket();
        $basket->getWidgetData();
        $params['widget'] = $basket->widget;
        
//        Tools::PA($params); die();
        
        $params = $this->mkconfig( $params );
        $this->DisplayBasket( $params );
    }

    private function mkconfig ( $params ) 
    {
        return array_merge( $this->config, $params );
    }

    private function DisplayBasket( $params ) 
    {
        $return = array(
            'template' => "widgets/pbasket.tpl",
            'data'     => $params
        );
        $this->output = $return;
    }

}
