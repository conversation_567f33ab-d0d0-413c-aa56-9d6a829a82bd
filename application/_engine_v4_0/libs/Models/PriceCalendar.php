<?php

namespace App\Libs\Models;

use App\Models\Model;

/**
 * @property int id
 * @property \DateTime sell_date_from
 * @property \DateTime sell_date_to
 * @property string id_offer
 * @property string ptype
 * @property string id_product
 * @property string id_variant
 * @property float price
 * @property int limit
 * @property int left
 * @property string configuration
 * @property int status
 * @property \DateTime ts
 */
class PriceCalendar extends Model
{
    protected string $_table = 'ts_price_calendar';
    protected string $calendar_table = 'ts_cennik_kalendarz';

    protected array $config = [];

    const STATUSES = [
        -1 => "Nowy",
        0 => "Nieaktywny",
        1 => "Wygenerowany",
        2 => "Przygotowany do generowania",
    ];


    public function getConfiguration($property = null)
    {
        if (null === $property) {
            return $this->config;
        }

        return $this->config[$property] ?? null;
    }

    public function setConfiguration(array $configuration): self
    {
        $this->config = $configuration;
        return $this;
    }

    public function mergeConfiguration(array $configuration): self
    {
        $this->config = array_merge($this->config, $configuration);
        return $this;
    }

    public function save(): ?int
    {
        $this->attributes['configuration'] = json_encode($this->config);
        return parent::save();
    }

    public function hydrate(array $data)
    {
        return parent::hydrate($data)->setConfiguration(json_decode($data['configuration'], true));
    }

    public function asArray(): array
    {
        return array_merge(parent::asArray(), ['configuration' => $this->getConfiguration()]);
    }

    public function getCalendar(): array
    {
        $calendarId = $this->attributes['id'] ?? $this->id ?? null;
        $res = $this->getHandler()
            ->query("SELECT * FROM {$this->calendar_table} WHERE id_calendar = {$calendarId} AND status = 1", MYSQLI_ASSOC);

        return $res->fetch_all(MYSQLI_ASSOC);
    }

    /**
     * Generuje kalendarz cen na podstawie konfiguracji
     * @return bool
     */
    public function generateCalendar(): bool
    {
        $calendarId = $this->attributes['id'] ?? $this->id ?? null;
        if (empty($calendarId)) {
            $this->error = 'Brak ID kalendarza cen';
            return false;
        }

        $config = $this->getConfiguration();
        if (empty($config)) {
            $this->error = 'Brak konfiguracji kalendarza';
            return false;
        }

        // Usuń istniejące wpisy kalendarza
        $this->clearCalendar();

        $passDateType = $config['pass_date_type'] ?? '';

        switch ($passDateType) {
            case 'weekdays':
                return $this->generateWeekdaysCalendar($config);
            case 'specific_dates':
                return $this->generateSpecificDatesCalendar($config);
            default:
                $this->error = 'Nieznany typ kalendarza: ' . $passDateType;
                return false;
        }
    }

    /**
     * Usuwa istniejące wpisy kalendarza
     */
    private function clearCalendar(): void
    {
        $calendarId = $this->attributes['id'] ?? $this->id ?? null;
        $this->getHandler()->query("DELETE FROM {$this->calendar_table} WHERE id_calendar = {$calendarId}");
    }

    /**
     * Generuje kalendarz dla dni tygodnia
     * @param array $config
     * @return bool
     */
    private function generateWeekdaysCalendar(array $config): bool
    {
        $weekdays = $config['pass_weekdays'] ?? [];
        if (empty($weekdays)) {
            $this->error = 'Brak konfiguracji dni tygodnia';
            return false;
        }

        $sellDateFrom = new \DateTime($this->sell_date_from);
        $sellDateTo = new \DateTime($this->sell_date_to);

        $current = clone $sellDateFrom;
        $generatedCount = 0;

        while ($current <= $sellDateTo) {
            $dayName = strtolower($current->format('l'));

            if (isset($weekdays[$dayName])) {
                $dayConfig = $weekdays[$dayName];

                // Sprawdź czy dzień jest włączony (domyślnie true jeśli nie ma pola enabled)
                $enabled = !isset($dayConfig['enabled']) || $dayConfig['enabled'] === '1' || $dayConfig['enabled'] === true;

                if ($enabled && isset($dayConfig['time_from']) && isset($dayConfig['time_to'])) {
                    $timeFrom = $dayConfig['time_from'];
                    $timeTo = $dayConfig['time_to'];

                    $datetimeStart = $current->format('Y-m-d') . ' ' . $timeFrom . ':00';
                    $datetimeStop = $current->format('Y-m-d') . ' ' . $timeTo . ':00';

                    if ($this->insertCalendarEntry($datetimeStart, $datetimeStop)) {
                        $generatedCount++;
                    }
                }
            }

            $current->add(new \DateInterval('P1D'));
        }

        if ($generatedCount > 0) {
            $this->status = 1; // Wygenerowany
            $this->save();
            return true;
        }

        $this->error = 'Nie wygenerowano żadnych wpisów kalendarza';
        return false;
    }

    /**
     * Generuje kalendarz dla konkretnych dat
     * @param array $config
     * @return bool
     */
    private function generateSpecificDatesCalendar(array $config): bool
    {
        $specificDates = $config['pass_specific_dates'] ?? [];
        if (empty($specificDates)) {
            $this->error = 'Brak konfiguracji konkretnych dat';
            return false;
        }

        $generatedCount = 0;

        foreach ($specificDates as $dateConfig) {
            if (isset($dateConfig['date']) && isset($dateConfig['time_from']) && isset($dateConfig['time_to'])) {
                $date = $dateConfig['date'];
                $timeFrom = $dateConfig['time_from'];
                $timeTo = $dateConfig['time_to'];

                $datetimeStart = $date . ' ' . $timeFrom . ':00';
                $datetimeStop = $date . ' ' . $timeTo . ':00';

                if ($this->insertCalendarEntry($datetimeStart, $datetimeStop)) {
                    $generatedCount++;
                }
            }
        }

        if ($generatedCount > 0) {
            $this->status = 1; // Wygenerowany
            $this->save();
            return true;
        }

        $this->error = 'Nie wygenerowano żadnych wpisów kalendarza';
        return false;
    }

    /**
     * Wstawia wpis do kalendarza
     * @param string $datetimeStart
     * @param string $datetimeStop
     * @return bool
     */
    private function insertCalendarEntry(string $datetimeStart, string $datetimeStop): bool
    {
        $db = $this->getHandler();

        $sql = "INSERT INTO {$this->calendar_table}
                (id_calendar, id_offer, id_product, datetime_start, datetime_stop, price, id_variant, status, ts)
                VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW())";

        $stmt = $db->prepare($sql);
        if (!$stmt) {
            $this->error = 'Błąd przygotowania zapytania: ' . $db->error;
            return false;
        }

        $calendarId = $this->attributes['id'] ?? $this->id ?? null;
        $stmt->bind_param('issssds',
            $calendarId,
            $this->id_offer,
            $this->id_product,
            $datetimeStart,
            $datetimeStop,
            $this->price,
            $this->id_variant
        );

        if (!$stmt->execute()) {
            $this->error = 'Błąd wykonania zapytania: ' . $stmt->error;
            return false;
        }

        return true;
    }

    /**
     * Usuwa kalendarz cen wraz z wszystkimi wygenerowanymi wpisami
     * @return bool
     */
    public function deleteCalendar(): bool
    {
        $calendarId = $this->attributes['id'] ?? $this->id ?? null;
        if (empty($calendarId)) {
            $this->error = 'Brak ID kalendarza cen';
            return false;
        }

        $db = $this->getHandler();

        // Rozpocznij transakcję
        $db->autocommit(false);

        try {
            // Usuń wpisy kalendarza
            $stmt1 = $db->prepare("DELETE FROM {$this->calendar_table} WHERE id_calendar = ?");
            if (!$stmt1) {
                throw new \Exception('Błąd przygotowania zapytania usuwania kalendarza: ' . $db->error);
            }
            $stmt1->bind_param('i', $calendarId);
            if (!$stmt1->execute()) {
                throw new \Exception('Błąd usuwania wpisów kalendarza: ' . $stmt1->error);
            }

            // Usuń konfigurację kalendarza
            $stmt2 = $db->prepare("DELETE FROM {$this->_table} WHERE id = ?");
            if (!$stmt2) {
                throw new \Exception('Błąd przygotowania zapytania usuwania konfiguracji: ' . $db->error);
            }
            $stmt2->bind_param('i', $calendarId);
            if (!$stmt2->execute()) {
                throw new \Exception('Błąd usuwania konfiguracji kalendarza: ' . $stmt2->error);
            }

            // Zatwierdź transakcję
            $db->commit();
            $db->autocommit(true);

            return true;

        } catch (\Exception $e) {
            // Wycofaj transakcję w przypadku błędu
            $db->rollback();
            $db->autocommit(true);
            $this->error = $e->getMessage();
            return false;
        }
    }

    /**
     * Usuwa tylko wpisy kalendarza (bez usuwania konfiguracji)
     * @return bool
     */
    public function clearCalendarOnly(): bool
    {
        $calendarId = $this->attributes['id'] ?? $this->id ?? null;
        if (empty($calendarId)) {
            $this->error = 'Brak ID kalendarza cen';
            return false;
        }

        $db = $this->getHandler();
        $stmt = $db->prepare("DELETE FROM {$this->calendar_table} WHERE id_calendar = ?");
        if (!$stmt) {
            $this->error = 'Błąd przygotowania zapytania: ' . $db->error;
            return false;
        }

        $stmt->bind_param('i', $calendarId);
        if (!$stmt->execute()) {
            $this->error = 'Błąd usuwania wpisów kalendarza: ' . $stmt->error;
            return false;
        }

        // Zaktualizuj status na "Nowy"
        $this->status = -1;
        $this->save();

        return true;
    }


}