<?php

namespace App\Libs\Models;

use App\Models\Model;

/**
 * @property int id
 * @property \DateTime sell_date_from
 * @property \DateTime sell_date_to
 * @property string id_offer
 * @property string ptype
 * @property string id_product
 * @property string id_variant
 * @property float price
 * @property int limit
 * @property int left
 * @property string configuration
 * @property int status
 * @property \DateTime ts
 */
class PriceCalendar extends Model
{
    protected string $_table = 'ts_price_calendar';
    protected string $calendar_table = 'ts_cennik_kalendarz';

    protected array $config = [];

    const STATUSES = [
        -1 => "Nowy",
        0 => "Nieaktywny",
        1 => "Wygenerowany",
        2 => "Przygotowany do generowania",
    ];


    public function getConfiguration($property = null)
    {
        if (null === $property) {
            return $this->config;
        }

        return $this->config[$property] ?? null;
    }

    public function setConfiguration(array $configuration): self
    {
        $this->config = $configuration;
        return $this;
    }

    public function mergeConfiguration(array $configuration): self
    {
        $this->config = array_merge($this->config, $configuration);
        return $this;
    }

    public function save(): ?int
    {
        $this->attributes['configuration'] = json_encode($this->config);
        return parent::save();
    }

    public function hydrate(array $data)
    {
        return parent::hydrate($data)->setConfiguration(json_decode($data['configuration'], true));
    }

    public function asArray(): array
    {
        return array_merge(parent::asArray(), ['configuration' => $this->getConfiguration()]);
    }

    public function getCalendar(): array
    {
        $res = $this->getHandler()
            ->query("SELECT * FROM {$this->calendar_table} WHERE id_calendar = {$this->id} AND status = 1", MYSQLI_ASSOC);

        return $res->fetch_all(MYSQLI_ASSOC);
    }


}