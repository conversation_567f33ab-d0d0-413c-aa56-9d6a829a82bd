<?php

/**
 * <PERSON><PERSON><PERSON> dekoratora Smarty
 * @package Application
 * @subpackage Decorator
 */
class DecoratorSmartyFile{
       
       public $app = null;
       protected $tpl = null;
       protected $decorator_payload = array();
       protected $decorator_app_payload = array();
       protected $widgets_data = array();
       protected $module_data = array();
       protected $parts_data = array();
       public $output;
    
       /**
        * Dekorator Smarty (3)
        * @param object $app Aplikacja (Dependecy Injection (DI))
        */
       public function __construct($app) {
           $this->app = $app;
           $this->decorator_app_payload = $this->app->GetDecoratoAppData();
           $this->decorator_payload = $this->app->GetDecoratorData();
           $this->widgets_data = $this->app->GetDecoratorWidgetsData();
           $this->parts_data = $this->app->GetPartsData();
           $this->DecoratorSmarty();
    }
    
    public function DecoratorSmarty(){
        define('SMARTY_DIR', ENSETTINGSDIR."decorator/Smarty/");
        require_once(ENSETTINGSDIR . "libs/SmartySetup.class.php");
        $this->tpl = (defined('ADMINDIR') ? new SmartySetup : new SmartySetupWebsite);  
        foreach($this->decorator_app_payload as $k=>$v){
            $this->tpl->assign($k,$v);
        }

        // parts
        foreach ($this->parts_data as $part=>$params){
            $this->tpl->assign('part',$params['data']);
            $this->tpl->assign($part,$this->tpl->fetch($params['template']));
        }
        
        // widgets
        if(!is_array($this->widgets_data)) $this->widgets_data = array();
        foreach($this->widgets_data as $widget=>$params){
            $this->tpl->assign('params',$params['data']);
            $this->tpl->assign($widget,$this->tpl->fetch($params['template']));
        }
        
        foreach($this->decorator_payload as $k=>$v){
            $this->tpl->assign($k,$v);
        }
     
        // module
        if($dec = $this->app->GetDecoratorModule()){
            $this->tpl->assign($dec['var'],$this->tpl->fetch($dec['tpl']));
        }
        
        // finish
        $this->output = $this->tpl->fetch($this->app->GetTemplate());
        file_put_contents('output.html', $this->output);
    }
}