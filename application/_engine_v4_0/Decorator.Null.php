<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class DecoratorNull{
    public $output = null;
        
    
    /**
     * 
     * @param type $app
     * @return boolean
     */
       public function __construct() {
//           $this->output = join(',', $app->GetDecoratorData());
           return true;
    }

}