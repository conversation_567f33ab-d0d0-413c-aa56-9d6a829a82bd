<script>

  //  import moment from '../../assets/js/uncompressed/moment';

    $(document).ready(function () {

      var emitentPartners = [];
      var currentQRPartner = '{{$postback.partner_wydajacy}}';
      console.log('scripts loaded');

      $('#typ-produktu').on('change',function() {
        var sel = $(this).val();
        console.log(sel);
        if(!sel) return;
        var oferta = $('#oferta').val();
        if(oferta === 'all') return false;
        $.get('ajadmin.php?pricescalendar&a=opl&offerid='+oferta+'&ptype='+sel,function(resp){
          console.log(resp);
          if(resp.error){
            alert(resp.msg);
            $('select#produkt').html('').trigger('chosen:updated');
            return false;
          }
          var opt = "<option value=''>{{App::_Lang('wybierz typ produktu')}}</option>";

          $.each(resp.product,function(idx,el){
            opt += '<option value="'+ el.id+'">'+el.nazwa+'</option>';
          });
          $('select#produkt').html(opt).trigger('chosen:updated');
        },'json');
        console.log($('#przeznaczenie-kuponu').val());
      });

      $('#produkt').on('change',function() {
        var produkt = $(this).val();
        console.log(produkt);
        if(!produkt) {
          return
        }
        var oferta = $('#oferta').val();
        var ptype = $('#typ-produktu').val();
        $.get('ajadmin.php?pricescalendar&a=pvl&offerid='+oferta+'&ptype='+ptype+'&product='+produkt,function(resp){
          console.log(resp);
          if(resp.error){
            alert(resp.msg);
            $('select#produkt').html('').trigger('chosen:updated');
            return false;
          }
          var opt = "<option value=''>{{App::_Lang('wybierz wariant produktu')}}</option>";

          $.each(resp.variants,function(idx, el){
            opt += '<option value="'+ el.bsid+'" data-price="'+el.cena+'">'+el.nazwa+'</option>';
          });
          $('select#wariant').html(opt).trigger('chosen:updated');
        },'json');
      });

      $('#wariant').on('change',function() {
        let price = $('option:selected', this).first().attr('data-price') ?? 0;
        // var price = $('option:selected', this).prop('data-price');
        console.log(price);
        // return;
        let targetField = $('#base_price');
        if(!price) {
          $(targetField).html('0');
        } else {
          $(targetField).html(price);
        }
      });

      $('#oferta').on('change',function(){
        $('#typ-produktu').change();
      });

        $('input#ograniczone').click(function () {
            if ($(this).is(":checked")) {
                $('#ilosc_kart').prop('disabled', false);
            } else
                $('#ilosc_kart').prop('disabled', true);
        });



        $('form#edit').on('submit', function () {
            if (required()) {
                if ($('#numeracja').val() === 'all') {
                    alert('{{App::_Lang('Wybierz rodzaj numeracji!','Promocje')}}');
                    men = $('div#numeracja_chosen');
                    tt = men.offset().top;
                    men.toggleClass('chosen-container-active');
                    $('body').animate({scrollTop: tt}, 1000);
                    event.preventDefault();
                    return false;
                }
                if ($('#oferta').val() === 'all' && $('#zakres').val() !== 'ALL') {
                    alert('{{App::_Lang('Wybierz ofertę, której dotyczy kupon!','Promocje')}}');
                    $('div#oferta_chosen').toggleClass('chosen-container-active');
                    $('body').animate({scrollTop: $('div#oferta_chosen').offset().top}, 1000);
                    event.preventDefault();
                    return false;
                }
                if ($('#numeracja').val() === 'JEDNOSTKOWA' && $('#emisjakuponu').val() != 'OBCA') {
                    if (Number($('#volumen').val()) < 1) {
                        alert('{{App::_Lang('Wpisz ilość kuponów do wygenerowania!','Promocje')}}');
                        $('input#volumen').focus();
                        $('body').animate({scrollTop: $('input#volumen').offset().top}, 1000);
                        event.preventDefault();
                        return false;
                    }
                    console.log(Number($('#volumen').val()));
                }

                if($('#numeracja').val() === 'ZBIORCZA' && $('#kod-emisji-zbiorczej').val().length < 2) {
                  alert('{{App::_Lang('Wpisz kod emisji zbiorczej!','Promocje')}}');
                  return false;
                }

                return true;
            } else {
                alert("{{App::_Lang('Proszę uzupełnić pola','Promocje')}}");
                event.preventDefault();
                return false;
            }
        });

        $('button#aktywuj_btn').on('click', function () {
            var m = '#modal-aktywacja';
            var b = m + ' .modal-body';
            var t = m + ' .modal-title';
            var f = m + ' .modal-footer';
            $(m).modal();
        });

        $('button#akceptuj_btn').on('click', function () {
            var m = '#modal-akceptacja';
            var b = m + ' .modal-body';
            var t = m + ' .modal-title';
            var f = m + ' .modal-footer';
            $(b).html('{{App::_Lang('Chcesz zaakceptować ten kupon?','Promocje')}}<br>{{App::_Lang('Przed akceptacją kupon zostanie automatycznie zapisany.','Promocje')}}<br>{{App::_Lang('Po akceptacji nie ma możliwości edycji ustawień kuponu','Promocje')}}');
            $(t).html('<h2>{{App::_Lang('Akceptacja kuponu','Promocje')}}</h2>');
            $(m).modal();
        });




        $('#nieograniczone').on('change', function () {
            if ($(this).prop('checked')) {
                console.log('checked');
                $('#wielkosc-emisji-row,#wartosc-kuponow-row').hide();
              $('#volumen').prop('required', false);
            }
            else {
                console.log('unchecked');
                $('#wielkosc-emisji-row,#wartosc-kuponow-row').show();
                if($('#emisjakuponu').val() !== 'OBCA') {
                   $('#volumen').prop('required', true);
                }
            }
        });


        $('#typ_wartosci').on('change', function (ev){
          if($(this).val() === 'procentowy') {
            $('#add_to_all_check').prop('disabled', false);
          } else {
            $('#add_to_all_check').prop('checked', false).prop('disabled', true);
          }
        });

        $("#dataod").daterangepicker({
            singleDatePicker: true,
            timePicker: true,
            timePicker12Hour: false,
            format: 'YYYY-MM-DD HH:mm',
            startDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            minDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            timePickerIncrement: 1
        });

        $('#dataemisji').on('apply.daterangepicker', function (ev, picker) {
            $('#dataod').data('daterangepicker').setOptions(
                    {
                        singleDatePicker: true,
                        timePicker: true,
                        timePicker12Hour: false,
                        format: 'YYYY-MM-DD HH:mm',
                        startDate: picker.startDate.format('YYYY-MM-DD HH:mm'),
                        minDate: picker.startDate.format('YYYY-MM-DD HH:mm'),
                        timePickerIncrement: 1
                    })
        });

        $("#datado").daterangepicker({
            singleDatePicker: true,
            format: 'YYYY-MM-DD HH:mm',
            timePicker: true,
            timePicker12Hour: false,
            startDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            minDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            timePickerIncrement: 1
        });

        $("#dataod").on('apply.daterangepicker', function (ev, picker) {
            $("#datado").data('daterangepicker').setOptions({
                singleDatePicker: true,
                format: 'YYYY-MM-DD HH:mm',
                timePicker: true,
                timePicker12Hour: false,
                startDate: picker.startDate.format('YYYY-MM-DD HH:mm'),
                minDate: picker.startDate.format('YYYY-MM-DD HH:mm'),
                timePickerIncrement: 1
            });
        });
        
        $('#emitent').change();

        // Pass Configuration Form Functionality
        initPassConfiguration();

        // Handle "Save and Generate" button
        $('#save_and_generate').on('click', function() {
            var calendarId = $(this).data('id');
            if (!calendarId) {
                alert('{{App::_Lang("Brak ID kalendarza")}}');
                return;
            }

            if (confirm('{{App::_Lang("Czy chcesz zapisać i wygenerować kalendarz cen?")}}')) {
                // First save the form
                var form = $('#edit');
                var formData = form.serialize();

                $.post(form.attr('action'), formData, function(response) {
                    // After saving, generate calendar
                    window.location.href = 'admin.php?pricescalendar&a=generate&id=' + calendarId;
                }).fail(function() {
                    alert('{{App::_Lang("Błąd podczas zapisywania")}}');
                });
            }
        });

    });

    // Pass Configuration Functions
    function initPassConfiguration() {
        // Handle date type radio button changes
        $('input[name="configuration[pass_date_type]"]').on('change', function() {
            var selectedType = $(this).val();
            if (selectedType === 'specific_dates') {
                $('#specific-dates-section').show();
                $('#weekdays-section').hide();
                // Enable specific dates inputs
                $('#specific-dates-section input').prop('disabled', false);
                // Disable weekdays inputs
                $('#weekdays-section input').prop('disabled', true);
            } else if (selectedType === 'weekdays') {
                $('#specific-dates-section').hide();
                $('#weekdays-section').show();
                // Disable specific dates inputs
                $('#specific-dates-section input').prop('disabled', true);
                // Enable weekdays inputs
                $('#weekdays-section input').prop('disabled', false);
            }
        });

        // Add new specific date row
        $('#add-specific-date').on('click', function() {
            var container = $('#specific-dates-container');
            var currentRows = container.find('.specific-date-row').length;
            var newIndex = currentRows;

            var newRow = $('<div class="specific-date-row" data-index="' + newIndex + '">' +
                '<div class="row mb-10">' +
                    '<div class="col-md-4">' +
                        '<div class="input-group">' +
                            '<span class="input-group-addon">{{App::_Lang("Data")}}</span>' +
                            '<input type="date" class="form-control specific-date-input" name="configuration[pass_specific_dates][' + newIndex + '][date]" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-3">' +
                        '<div class="input-group">' +
                            '<span class="input-group-addon">{{App::_Lang("Od")}}</span>' +
                            '<input type="time" class="form-control" name="configuration[pass_specific_dates][' + newIndex + '][time_from]" value="09:00" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-3">' +
                        '<div class="input-group">' +
                            '<span class="input-group-addon">{{App::_Lang("Do")}}</span>' +
                            '<input type="time" class="form-control" name="configuration[pass_specific_dates][' + newIndex + '][time_to]" value="17:00" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<button type="button" class="btn btn-danger btn-sm remove-specific-date">' +
                            '<i class="fa fa-minus"></i> {{App::_Lang("Usuń")}}' +
                        '</button>' +
                    '</div>' +
                '</div>' +
            '</div>');

            container.append(newRow);
            updateRemoveButtons();
        });

        // Remove specific date row
        $(document).on('click', '.remove-specific-date', function() {
            $(this).closest('.specific-date-row').remove();
            reindexSpecificDates();
            updateRemoveButtons();
        });

        // Handle weekday checkbox changes
        $(document).on('change', '.weekday-checkbox', function() {
            var isChecked = $(this).is(':checked');
            var weekdayRow = $(this).closest('.weekday-row');
            var timeInputs = weekdayRow.find('.weekday-times');

            if (isChecked) {
                timeInputs.show();
                timeInputs.find('input').prop('required', true);
            } else {
                timeInputs.hide();
                timeInputs.find('input').prop('required', false);
            }
        });

        // Initialize the form state
        $('input[name="pass_date_type"]:checked').trigger('change');
    }

    function updateRemoveButtons() {
        var rows = $('#specific-dates-container .specific-date-row');
        rows.each(function(index) {
            var removeBtn = $(this).find('.remove-specific-date');
            if (rows.length <= 1) {
                removeBtn.hide();
            } else {
                removeBtn.show();
            }
        });
    }

    function reindexSpecificDates() {
        $('#specific-dates-container .specific-date-row').each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('input[name*="pass_specific_dates"]').each(function() {
                var name = $(this).attr('name');
                var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                $(this).attr('name', newName);
            });
        });
    }
</script>