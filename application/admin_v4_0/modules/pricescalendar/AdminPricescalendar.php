<?php

class AdminPricescalendar extends AdminModule
{
    protected $headerparams = array();
    protected $topparams = array();
    protected $adminnavparams = array();
    protected $footerparams = array();
    protected $en = null;
    protected $partners = null;

    public function __construct($app)
    {
        $this->app = &$app;
        $this->en = Database::activate();
        Tools::activate($this->app->urlparam);

        switch ($this->app->urlparam['a']) {
            case 'list':
                $this->return = $this->_r('list');

                break;

            case 'listajax':
                $this->return = $this->_r('AjaxList');
                break;

            case 'add':
                $this->return = $this->_r('AddCalendar');
                break;
            case 'edit':
                $this->return = $this->_r('EditCalendar');
                break;
            case 'save':
                $this->return = $this->_r('Save');

                break;
            case 'generate':
                $this->return = $this->_r('GenerateCalendar');
                break;

            case 'delete':

                break;
            case 'opl':
                $offerid = $_REQUEST['offerid'];
                $ptype = $_REQUEST['ptype'];
                $this->ajaxGetOfferProductsList($offerid, $ptype);
                break;
            case 'pvl':
                $offerid = $_REQUEST['offerid'];
                $ptype = $_REQUEST['ptype'];
                $product = $_REQUEST['product'];
                $this->ajaxGetProductVariantsList($offerid, $ptype, $product);
                break;

            case 'report':

                break;

            case 'post':
                $this->post();
                break;

            case 'aol':
                $this->ajaxGetAcceptedOfferList();
                break;
            case 'op':
                $this->ajaxGetOfferPartners();
                break;
            case 'opp':
                $this->ajaxGetOsrodekPartners();
                break;
            case 'getemisja':
                $this->_r('getEmisja');
                break;
            case 'getcouponlist':
                $this->_r('GetCouponList');
                break;
            case 'getpdflist':
                $this->_r('GetPdfList');
                break;
            default:
                $this->return = $this->_r('DisplayListCoupons');
                break;
        }
    }

    public function save()
    {
        $pc = null;
        switch ($_POST['todo']) {
            case 'add':
                $pc = $this->create($_POST);
                break;
            case 'edit':
                $pc = $this->update($_POST, $_POST['cid']);
                break;
        }

        redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $pc->id);
    }

    public function saveAndGenerate($cid)
    {
        $pc = $this->update($_POST, $_POST['cid']);
    }

    /**
     * Generuje kalendarz cen dla istniejącego kalendarza
     */
    public function GenerateCalendar()
    {
        $id = $this->app->urlparam['id'] ?? null;
        if (!$id) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Brak ID kalendarza';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->findOne($id);

        if (!$pc->id && !$pc->attributes['id']) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Kalendarz nie został znaleziony';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $result = $pc->generateCalendar();

        if ($result) {
            $calendar = $pc->getCalendar();
            $_SESSION['notify'] = 'success';
            $_SESSION['notifytext'] = 'Kalendarz został wygenerowany pomyślnie. Utworzono ' . count($calendar) . ' wpisów.';
        } else {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Błąd generowania kalendarza: ' . $pc->error;
        }

        redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $id);
    }

    protected function create($data): \App\Libs\Models\PriceCalendar
    {
        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->sell_date_from = (new \DateTime($data['sell_date_from']))->format('Y-m-d H:i');
        $pc->sell_date_to = (new \DateTime($data['sell_date_to']))->format('Y-m-d H:i');
        $pc->id_offer = $data['id_offer'];
        $pc->ptype = $data['ptype'];
        $pc->id_product = $data['id_product'];
        $pc->id_variant = $data['id_variant'];
        $pc->price = number_format($data['actual_price'] * 100 / 100, 2, '.', '');
        $pc->limit = (int)($data['nieograniczone'] ?? 0) === 1 ? -1 : $data['limit'];
        $pc->left = $pc->limit;
        $pc->setConfiguration($data['configuration']);
        $pc->status = -1;
        $pc->save();
        return $pc;
    }


    protected function update($data, $id): \App\Libs\Models\PriceCalendar
    {
        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->findOne($id);
        $pc->sell_date_from = (new \DateTime($data['sell_date_from']))->format('Y-m-d H:i');
        $pc->sell_date_to = (new \DateTime($data['sell_date_to']))->format('Y-m-d H:i');
        $pc->id_offer = $data['id_offer'];
        $pc->ptype = $data['ptype'];
        $pc->id_product = $data['id_product'];
        $pc->id_variant = $data['id_variant'];
        $pc->price = number_format($data['actual_price'] * 100 / 100, 2, '.', '');
        $pc->limit = (int)($data['nieograniczone'] ?? 0) === 1 ? -1 : $data['limit'];
        $pc->left = $pc->limit;
        $pc->setConfiguration($data['configuration']);
        $pc->status = $data['active'] ?? -1;
        $pc->save();
        return $pc;
    }

    public function list()
    {
        $this->headerparams['meta']['title'] = $this->app->Lang('Lista cen',
                'Calendar') . " | ADMIN " . $this->headerparams['meta']['title'];
        Tools::activate($this->app->urlparam);
        $redirect = urldecode($_GET['redirect']);
        if ($redirect == '') {
            $redirect = SITE_URL . 'admin.php?pricescalendar&a=list';
            $redirect = urlencode($redirect);
        }
        $params['offers'] = array_merge(Tools::GetPartners(1), Tools::GetPartners(6));
        $params['statusy'] = array(
            array('id' => -1, 'nazwa' => 'nowa'),
            array('id' => 0, 'nazwa' => 'zakończona'),
            array('id' => 1, 'nazwa' => 'aktywna')
        );
        $params['data_od'] = $this->app->urlparam['dataod'];
        $params['data_do'] = $this->app->urlparam['datado'];
        $params['status'] = $this->app->urlparam['status'];
        $params['wlasciciel'] = $this->app->urlparam['wlasciciel'];
        if ($_SESSION['notify']) {
            $this->app->ADD($_SESSION['notify'], true);
            $this->app->ADD('notifytext', $_SESSION['notifytext']);
            unset($_SESSION['notify']);
            unset($_SESSION['notifytext']);
        }
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list(true, true);
        $this->app->ADD('oferty', $oferty);
//        $this->app->ADD('partnerzy', array_merge(Tools::GetPartners(2), Tools::GetPartners(3), Tools::GetPartners(5)));
//        $this->app->ADD('params', $params);
//        $this->app->ADD('product', 'coupon');
        $this->app->AddDecoratorData('module', MODULE);
        $this->adminnavparams['selected'] = 'calendar.list';
        $this->app->ADD('tablescript', 'modules/pricescalendar/calendar_table.tpl');
        $this->app->ADD('headertitle', 'Kalendarz');
        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/list_calendar.tpl");
        $this->makeWidgets();

        $this->app->SetTemplate("modules/" . MODULE . "/page.tpl");
        $this->makeWidgets();
    }

    public function AjaxList()
    {
        $this->headerparams['meta']['title'] = $this->app->Lang('Lista cen',
                'Calendar') . " | ADMIN " . $this->headerparams['meta']['title'];
        Tools::activate($this->app->urlparam);
        $redirect = urldecode($_GET['redirect']);
        if ($redirect == '') {
            $redirect = SITE_URL . 'admin.php?promotions&a=calendar-list';
            $redirect = urlencode($redirect);
        }
        $params['sprzedawcy'] = array_merge(Tools::GetPartners(1), Tools::GetPartners(6));
        $params['statusy'] = array(
            array('id' => -1, 'nazwa' => 'nowa'),
            array('id' => 0, 'nazwa' => 'zakończona'),
            array('id' => 1, 'nazwa' => 'aktywna')
        );
        $params['cardserial'] = $this->app->urlparam['cardserial'];
        $params['data_od'] = $this->app->urlparam['dataod'];
        $params['data_do'] = $this->app->urlparam['datado'];
        $params['typkarty'] = $this->app->urlparam['typkarty'];
        $params['sprzedawca'] = $this->app->urlparam['sprzedawca'];
        $params['status'] = $this->app->urlparam['status'];
        $params['wlasciciel'] = $this->app->urlparam['wlasciciel'];
        if ($_SESSION['notify']) {
            $this->app->ADD($_SESSION['notify'], true);
            $this->app->ADD('notifytext', $_SESSION['notifytext']);
            unset($_SESSION['notify']);
            unset($_SESSION['notifytext']);
        }
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list(true, true);
        $this->app->ADD('oferty', $oferty);
        $this->app->ADD('partnerzy', array_merge(Tools::GetPartners(2), Tools::GetPartners(3), Tools::GetPartners(5)));
        $this->app->ADD('params', $params);
        $this->app->ADD('product', 'coupon');
        $this->app->AddDecoratorData('module', MODULE);
        $this->adminnavparams['selected'] = 'coupons.list';
        $this->app->ADD('tablescript', 'modules/promotions/coupons_table.tpl');
        $this->app->ADD('headertitle', 'COUPONS');
        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/list_coupons.tpl");
        $this->makeWidgets();

        $this->app->SetTemplate("modules/" . MODULE . "/page.tpl");
        $this->makeWidgets();
    }

    /**
     * Dodawanie kalendarza do produktu
     */
    public function AddCalendar()
    {
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list();
        $this->app->ADD('oferty', $oferty);
        $this->adminnavparams['selected'] = 'calendar.list';
        $this->app->ADD('section', 'calendar');
        $this->app->ADD('todo', 'add');
        $this->app->SetTemplate('modules/pricescalendar/edit_calendar.tpl');
        $this->makeWidgets();
    }

    public function EditCalendar()
    {
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list();
        $this->app->ADD('oferty', $oferty);
        $this->adminnavparams['selected'] = 'calendar.list';
        $this->app->ADD('section', 'calendar');
        $this->app->ADD('todo', 'edit');
        $item = new \App\Libs\Models\PriceCalendar();
        $item->findOne($this->app->urlparam['id']);
        $this->app->ADD('postback', $item->asArray());
        $this->app->ADD('cid', $item->id);
        $this->app->ADD('proferty', $this->getOfferProductsList($item->id_offer, $item->ptype));
        $this->app->ADD('variants', $this->getProductsVariantsList($item->id_offer, $item->ptype, $item->id_product, true));

        // Dodaj wygenerowany kalendarz jeśli istnieje
        $calendar = $item->getCalendar();
        $this->app->ADD('generated_calendar', $calendar);

//        dd($this->app->GetDecoratorData());
        $this->app->SetTemplate('modules/pricescalendar/edit_calendar.tpl');
        $this->makeWidgets();
    }

    protected function ajaxGetOfferProductsList($offerid, $ptype)
    {
        $resp['error'] = false;
        $error = [];
        $produkty = [];

        if (false === Mymongo::isValidMongoid($offerid)) {
            $error[] = $this->app->Lang('Błąd MGO', 'Promocje');
        }

        if (false === preg_match('/[a-zA-Z]/', $ptype)) {
            $error[] = $this->app->Lang('Błąd PTYPE', 'Promocje');
        }

        if (count($error) > 0) {
            $resp['error'] = true;
            $resp['msg'] = implode('\n', $error);
            echo json_encode($resp);
            die();
        }

        $products = $this->getOfferProductsList($offerid, $ptype);

        if ($products['error']) {
            echo json_encode($products);
            die();
        }

        $resp['product'] = $products;
        echo json_encode($resp);
        die();
    }

    protected function ajaxGetProductVariantsList($offerid, $ptype, $product)
    {
        $resp['error'] = false;
        $error = [];
        $variants = [];

        if (false === Mymongo::isValidMongoid($offerid)) {
            $error[] = $this->app->Lang('Błąd MGO', 'Promocje');
        }

        if (false === preg_match('/[a-zA-Z]/', $ptype)) {
            $error[] = $this->app->Lang('Błąd PTYPE', 'Promocje');
        }

        if (count($error) > 0) {
            $resp['error'] = true;
            $resp['msg'] = implode('\n', $error);
            echo json_encode($resp);
            die();
        }


        $variants = $this->getProductsVariantsList($offerid, $ptype, $product, true);

        if ($variants['error']) {
            echo json_encode($variants);
            die();
        }

//        $mgo = Mymongo::activate();
//        switch ($ptype) {
//            case 'transport':
//                $ptype = 'przewoznicy';
//                $cursor = $mgo->mget_product_list($offerid, $ptype, true);
//                $check = $this->checkProductsResponse($cursor, $ptype);
//
//                if ($check['error']) {
//                    echo json_encode($check);
//                    die();
//                }
//
//                foreach ($cursor['przewoznicy'] as $v) {
//                    $produkty[] = array('id' => $v['hash'], 'nazwa' => $v['produkt_nazwa']);
//                }
//                break;
//
//
//            default:
//                $cursor = $mgo->mget_product_list($offerid, $ptype, true);
//                $check = $this->checkProductsResponse($cursor, $ptype);
//
//                if ($check['error']) {
//                    echo json_encode($check);
//                    die();
//                }
//
//                foreach ($cursor[$ptype] as $v) {
//                    if ($v['identyfikator'] === $product) {
//                        foreach ($v['wariant_cenowy'] as $variant) {
//                            $variants[] = [
//                                'bsid' => $variant['bsid'],
//                                'nazwa' => $variant['nazwa'],
//                                'cena' => $variant['cena']['brutto']
//                            ];
//                        }
//                    }
//                }
//                break;
//        }

        $resp['variants'] = $variants;
        echo json_encode($resp);
        die();
    }

    protected function checkProductsResponse(array $cursor, $ptype): array
    {
        $resp['error'] = false;
        if (!is_array($cursor)) {
            $resp['error'] = true;
            $resp['msg'] = $this->app->Lang('Brak produktów', 'Promocje');
        }

        if (count($cursor[$ptype] ?? []) === 0) {
            $resp['error'] = true;
            $resp['msg'] = $this->app->Lang('Brak produktów', 'Promocje');
        }

        return $resp;
    }

    protected function getOfferProductsList($offerid, $ptype, bool $full = false)
    {
        $mgo = Mymongo::activate();
        switch ($ptype) {
            case 'transport':
                $ptype = 'przewoznicy';
                $cursor = $mgo->mget_product_list($offerid, $ptype, true);
                $check = $this->checkProductsResponse($cursor, $ptype);

                if ($check['error']) {
                    return $check;
                }

                foreach ($cursor['przewoznicy'] as $v) {
                    $produkty[] = array('id' => $v['hash'], 'nazwa' => $v['produkt_nazwa']);
                }
                return $produkty;


            default:
                $cursor = $mgo->mget_product_list($offerid, $ptype, $full);
                $check = $this->checkProductsResponse($cursor, $ptype);

                if ($check['error']) {
                    return $check;
                }

                $ptype === 'karty' ? $suffix = 'podtytul' : $suffix = 'nazwa';
                foreach ($cursor[$ptype] as $v) {
                    if ($full) {
                        $produkty[] = $v;
                        continue;
                    }
                    $produkty[] = array('id' => $v['identyfikator'], 'nazwa' => $v[$suffix][LANG]);
                }
                return $produkty;
        }
    }

    protected function getProductsVariantsList($offerid, $ptype, $product, bool $full = false)
    {
        $check = $this->getOfferProductsList($offerid, $ptype, $full);

        if ($check['error']) {
            return $check;
        }
        $variants = [];
        foreach ($check as $v) {
            if ($v['identyfikator'] === $product) {
                foreach ($v['wariant_cenowy'] as $variant) {
                    $variants[] = [
                        'bsid' => $variant['bsid'],
                        'nazwa' => $variant['nazwa'],
                        'cena' => $variant['cena']['brutto']
                    ];
                }
            }
        }
//        dd($variants);
        return $variants;
    }

    private function makeWidgets()
    {
        $this->app->AWD('header', $this->headerparams);
        $this->app->AWD('top', $this->topparams);
        $this->app->AWD('adminnav', $this->adminnavparams);
        $this->app->AWD('footer', $this->footerparams);
    }
}