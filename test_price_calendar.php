<?php
/**
 * Test skrypt dla metody generateCalendar() w PriceCalendar
 */

require_once 'config.php';
require_once 'vendor/autoload.php';

use App\Libs\Models\PriceCalendar;

echo "=== Test generowania kalendarza cen ===\n\n";

// Test 1: Kalendarz dla dni tygodnia
echo "Test 1: Generowanie kalendarza dla dni tygodnia\n";
echo "------------------------------------------------\n";

$pc1 = new PriceCalendar();
$pc1->sell_date_from = '2025-08-18 00:00:00';
$pc1->sell_date_to = '2025-08-24 23:59:59';
$pc1->id_offer = 'test_offer_1';
$pc1->ptype = 'test';
$pc1->id_product = 'test_product_1';
$pc1->id_variant = 'test_variant_1';
$pc1->price = 99.99;
$pc1->limit = 100;
$pc1->left = 100;
$pc1->status = -1;

$weekdaysConfig = [
    'pass_date_type' => 'weekdays',
    'pass_weekdays' => [
        'monday' => [
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'tuesday' => [
            'enabled' => '1',
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'wednesday' => [
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'thursday' => [
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'friday' => [
            'enabled' => '1',
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'saturday' => [
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'sunday' => [
            'time_from' => '09:00',
            'time_to' => '17:00'
        ]
    ]
];

$pc1->setConfiguration($weekdaysConfig);

// Zapisz kalendarz
$id1 = $pc1->save();
if ($id1) {
    echo "✓ Kalendarz zapisany z ID: $id1\n";
    
    // Generuj kalendarz
    $result1 = $pc1->generateCalendar();
    if ($result1) {
        echo "✓ Kalendarz wygenerowany pomyślnie\n";
        
        // Sprawdź wygenerowane wpisy
        $calendar = $pc1->getCalendar();
        echo "✓ Wygenerowano " . count($calendar) . " wpisów kalendarza\n";
        
        // Pokaż pierwsze 3 wpisy
        echo "Pierwsze 3 wpisy:\n";
        for ($i = 0; $i < min(3, count($calendar)); $i++) {
            $entry = $calendar[$i];
            echo "  - {$entry['datetime_start']} do {$entry['datetime_stop']} (cena: {$entry['price']})\n";
        }
    } else {
        echo "✗ Błąd generowania kalendarza: " . $pc1->error . "\n";
    }
} else {
    echo "✗ Błąd zapisywania kalendarza: " . $pc1->error . "\n";
}

echo "\n";

// Test 2: Kalendarz dla konkretnych dat
echo "Test 2: Generowanie kalendarza dla konkretnych dat\n";
echo "---------------------------------------------------\n";

$pc2 = new PriceCalendar();
$pc2->sell_date_from = '2025-08-18 00:00:00';
$pc2->sell_date_to = '2025-08-30 23:59:59';
$pc2->id_offer = 'test_offer_2';
$pc2->ptype = 'test';
$pc2->id_product = 'test_product_2';
$pc2->id_variant = 'test_variant_2';
$pc2->price = 149.99;
$pc2->limit = 50;
$pc2->left = 50;
$pc2->status = -1;

$specificDatesConfig = [
    'pass_date_type' => 'specific_dates',
    'pass_specific_dates' => [
        [
            'date' => '2025-08-20',
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        [
            'date' => '2025-08-24',
            'time_from' => '09:00',
            'time_to' => '17:00'
        ]
    ]
];

$pc2->setConfiguration($specificDatesConfig);

// Zapisz kalendarz
$id2 = $pc2->save();
if ($id2) {
    echo "✓ Kalendarz zapisany z ID: $id2\n";
    
    // Generuj kalendarz
    $result2 = $pc2->generateCalendar();
    if ($result2) {
        echo "✓ Kalendarz wygenerowany pomyślnie\n";
        
        // Sprawdź wygenerowane wpisy
        $calendar2 = $pc2->getCalendar();
        echo "✓ Wygenerowano " . count($calendar2) . " wpisów kalendarza\n";
        
        // Pokaż wszystkie wpisy
        echo "Wszystkie wpisy:\n";
        foreach ($calendar2 as $entry) {
            echo "  - {$entry['datetime_start']} do {$entry['datetime_stop']} (cena: {$entry['price']})\n";
        }
    } else {
        echo "✗ Błąd generowania kalendarza: " . $pc2->error . "\n";
    }
} else {
    echo "✗ Błąd zapisywania kalendarza: " . $pc2->error . "\n";
}

echo "\n";

// Test 3: Błędna konfiguracja
echo "Test 3: Test błędnej konfiguracji\n";
echo "----------------------------------\n";

$pc3 = new PriceCalendar();
$pc3->sell_date_from = '2025-08-18 00:00:00';
$pc3->sell_date_to = '2025-08-24 23:59:59';
$pc3->id_offer = 'test_offer_3';
$pc3->ptype = 'test';
$pc3->id_product = 'test_product_3';
$pc3->id_variant = 'test_variant_3';
$pc3->price = 199.99;
$pc3->limit = 25;
$pc3->left = 25;
$pc3->status = -1;

$invalidConfig = [
    'pass_date_type' => 'invalid_type'
];

$pc3->setConfiguration($invalidConfig);

// Zapisz kalendarz
$id3 = $pc3->save();
if ($id3) {
    echo "✓ Kalendarz zapisany z ID: $id3\n";
    
    // Próbuj wygenerować kalendarz
    $result3 = $pc3->generateCalendar();
    if (!$result3) {
        echo "✓ Oczekiwany błąd: " . $pc3->error . "\n";
    } else {
        echo "✗ Nieoczekiwany sukces - powinien być błąd\n";
    }
} else {
    echo "✗ Błąd zapisywania kalendarza: " . $pc3->error . "\n";
}

echo "\n=== Koniec testów ===\n";

// Cleanup - usuń testowe dane
echo "Czyszczenie testowych danych...\n";
if (isset($id1)) {
    $db = Database::activate()->gethandler();
    $db->query("DELETE FROM ts_price_calendar WHERE id = $id1");
    $db->query("DELETE FROM ts_cennik_kalendarz WHERE id_calendar = $id1");
}
if (isset($id2)) {
    $db = Database::activate()->gethandler();
    $db->query("DELETE FROM ts_price_calendar WHERE id = $id2");
    $db->query("DELETE FROM ts_cennik_kalendarz WHERE id_calendar = $id2");
}
if (isset($id3)) {
    $db = Database::activate()->gethandler();
    $db->query("DELETE FROM ts_price_calendar WHERE id = $id3");
    $db->query("DELETE FROM ts_cennik_kalendarz WHERE id_calendar = $id3");
}
echo "✓ Testowe dane usunięte\n";
